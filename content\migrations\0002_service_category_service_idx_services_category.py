# Generated by Django 5.2.4 on 2025-07-31 07:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('content', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='service',
            name='category',
            field=models.CharField(choices=[('web_development', 'Web Development'), ('mobile_development', 'Mobile Development'), ('ui_ux_design', 'UI/UX Design'), ('consulting', 'Consulting'), ('maintenance', 'Maintenance & Support'), ('custom_software', 'Custom Software'), ('other', 'Other')], db_index=True, default='other', help_text='Service category for grouping and filtering', max_length=50),
        ),
        migrations.AddIndex(
            model_name='service',
            index=models.Index(fields=['category'], name='idx_services_category'),
        ),
    ]
