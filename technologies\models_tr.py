"""
Turkish models for technology management.

These models mirror the English technology models but store data in the Turkish database
to comply with Turkish data storage laws.
"""

from django.db import models
from django.core.validators import URLValidator, MinValueValidator, MaxValueValidator
from django.utils import timezone
from core.models import TurkishBaseModel, TurkishModelManager


class TechnologyCategoryTR(TurkishBaseModel):
    """
    Turkish version of TechnologyCategory model for organizing technologies into categories.
    Corresponds to the technology_categories_tr table in the Turkish database.
    """

    name = models.CharField(max_length=100, unique=True, verbose_name="Ad")
    display_name = models.CharField(
        max_length=120,
        help_text="Kullanıcı arayüzünde görüntülenecek ad",
        verbose_name="Görüntülenen Ad"
    )
    description = models.TextField(
        blank=True, null=True,
        help_text="Kategori açıklaması",
        verbose_name="Açıklama"
    )
    icon = models.Char<PERSON>ield(
        max_length=100,
        blank=True, null=True,
        help_text="CSS sınıfı veya ikon tanımlayıcısı",
        verbose_name="İkon"
    )
    color_code = models.CharField(
        max_length=7,
        blank=True, null=True,
        help_text="Kategori için hex renk kodu (örn: #FF5733)",
        verbose_name="Renk Kodu"
    )
    is_active = models.BooleanField(
        default=True,
        db_index=True,
        help_text="Bu kategorinin görüntülenip görüntülenmeyeceği",
        verbose_name="Aktif"
    )
    display_order = models.IntegerField(
        default=0,
        db_index=True,
        help_text="Kategorilerin görüntülenme sırası",
        verbose_name="Görüntüleme Sırası"
    )
    created_at = models.DateTimeField(default=timezone.now, verbose_name="Oluşturulma Tarihi")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Güncellenme Tarihi")

    objects = TurkishModelManager()

    class Meta:
        db_table = 'technology_categories_tr'
        verbose_name = 'Teknoloji Kategorisi'
        verbose_name_plural = 'Teknoloji Kategorileri'
        ordering = ['display_order', 'display_name']
        indexes = [
            models.Index(fields=['is_active'], name='idx_tech_cat_tr_active'),
            models.Index(fields=['display_order'], name='idx_tech_cat_tr_order'),
        ]

    def __str__(self):
        return self.display_name

    @property
    def technology_count(self):
        """Return the number of active technologies in this category."""
        return self.technologies_tr.filter(is_active=True).count()

    @property
    def featured_technology_count(self):
        """Return the number of featured technologies in this category."""
        return self.technologies_tr.filter(is_active=True, is_featured=True).count()


class TechnologyTR(TurkishBaseModel):
    """
    Turkish version of Technology model for individual technologies with proficiency levels and experience.
    Corresponds to the technologies_tr table in the Turkish database.
    """

    PROFICIENCY_CHOICES = [
        ('beginner', 'Başlangıç'),
        ('intermediate', 'Orta'),
        ('advanced', 'İleri'),
        ('expert', 'Uzman'),
    ]

    name = models.CharField(max_length=100, verbose_name="Ad")
    logo_url = models.URLField(
        max_length=500,
        blank=True, null=True,
        validators=[URLValidator()],
        help_text="Teknoloji logosu/ikonu URL'si",
        verbose_name="Logo URL"
    )
    category = models.ForeignKey(
        TechnologyCategoryTR,
        on_delete=models.CASCADE,
        related_name='technologies_tr',
        db_index=True,
        verbose_name="Kategori"
    )
    description = models.TextField(
        blank=True, null=True,
        help_text="Bu teknoloji ile ilgili deneyimimizin açıklaması",
        verbose_name="Açıklama"
    )
    proficiency_level = models.CharField(
        max_length=20,
        choices=PROFICIENCY_CHOICES,
        default='intermediate',
        db_index=True,
        help_text="Bu teknolojideki yeterlilik seviyemiz",
        verbose_name="Yeterlilik Seviyesi"
    )
    years_experience = models.IntegerField(
        blank=True, null=True,
        validators=[MinValueValidator(0), MaxValueValidator(50)],
        help_text="Bu teknoloji ile yıl cinsinden deneyim",
        verbose_name="Deneyim Yılı"
    )
    is_featured = models.BooleanField(
        default=False,
        db_index=True,
        help_text="Ana sayfada veya öne çıkan bölümlerde gösterilsin mi",
        verbose_name="Öne Çıkan"
    )
    is_active = models.BooleanField(
        default=True,
        db_index=True,
        help_text="Bu teknolojinin görüntülenip görüntülenmeyeceği",
        verbose_name="Aktif"
    )
    display_order = models.IntegerField(
        default=0,
        db_index=True,
        help_text="Kategori içindeki görüntülenme sırası",
        verbose_name="Görüntüleme Sırası"
    )
    created_at = models.DateTimeField(default=timezone.now, verbose_name="Oluşturulma Tarihi")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Güncellenme Tarihi")

    objects = TurkishModelManager()

    class Meta:
        db_table = 'technologies_tr'
        verbose_name = 'Teknoloji'
        verbose_name_plural = 'Teknolojiler'
        ordering = ['category__display_order', 'display_order', 'name']
        indexes = [
            models.Index(fields=['proficiency_level'], name='idx_tech_tr_proficiency'),
            models.Index(fields=['is_featured'], name='idx_tech_tr_featured'),
            models.Index(fields=['is_active'], name='idx_tech_tr_active'),
            models.Index(fields=['display_order'], name='idx_tech_tr_order'),
        ]
        unique_together = ['name', 'category']

    def __str__(self):
        return f"{self.name} ({self.category.display_name})"

    @property
    def proficiency_percentage(self):
        """Return proficiency as a percentage for display purposes."""
        proficiency_map = {
            'beginner': 25,
            'intermediate': 50,
            'advanced': 75,
            'expert': 95,
        }
        return proficiency_map.get(self.proficiency_level, 50)

    @property
    def experience_level(self):
        """Return a human-readable experience level based on years."""
        if not self.years_experience:
            return "Belirtilmemiş"
        
        if self.years_experience < 1:
            return "Yeni"
        elif self.years_experience < 2:
            return "1 yıl"
        elif self.years_experience < 5:
            return f"{self.years_experience} yıl"
        elif self.years_experience < 10:
            return f"{self.years_experience} yıl (Deneyimli)"
        else:
            return f"{self.years_experience} yıl (Uzman)"

    @property
    def proficiency_color(self):
        """Return a color code based on proficiency level."""
        color_map = {
            'beginner': '#ff6b6b',    # Red
            'intermediate': '#feca57', # Yellow
            'advanced': '#48dbfb',     # Blue
            'expert': '#0be881',       # Green
        }
        return color_map.get(self.proficiency_level, '#6c757d')

    @property
    def proficiency_icon(self):
        """Return an icon class based on proficiency level."""
        icon_map = {
            'beginner': 'fas fa-seedling',
            'intermediate': 'fas fa-chart-line',
            'advanced': 'fas fa-trophy',
            'expert': 'fas fa-crown',
        }
        return icon_map.get(self.proficiency_level, 'fas fa-code')

    def clean(self):
        """Custom validation for the model."""
        from django.core.exceptions import ValidationError

        # Validate years_experience based on proficiency_level
        if self.years_experience is not None and self.proficiency_level:
            min_years_map = {
                'beginner': 0,
                'intermediate': 1,
                'advanced': 3,
                'expert': 5,
            }
            
            min_years = min_years_map.get(self.proficiency_level, 0)
            if self.years_experience < min_years:
                raise ValidationError(
                    f"{self.get_proficiency_level_display()} seviyesi için en az {min_years} yıl deneyim gereklidir."
                )

    @classmethod
    def get_featured_technologies(cls, limit=None):
        """Return featured technologies ordered by category and display order."""
        queryset = cls.objects.filter(is_active=True, is_featured=True).select_related('category')
        if limit:
            queryset = queryset[:limit]
        return queryset

    @classmethod
    def get_by_category(cls, category_name=None):
        """Return technologies grouped by category."""
        queryset = cls.objects.filter(is_active=True).select_related('category')
        
        if category_name:
            queryset = queryset.filter(category__name=category_name)
        
        # Group by category
        from collections import defaultdict
        grouped = defaultdict(list)
        
        for tech in queryset:
            grouped[tech.category].append(tech)
        
        return dict(grouped)

    @classmethod
    def get_proficiency_stats(cls):
        """Return statistics about proficiency levels."""
        from django.db.models import Count
        
        return cls.objects.filter(is_active=True).values('proficiency_level').annotate(
            count=Count('id')
        ).order_by('proficiency_level')
