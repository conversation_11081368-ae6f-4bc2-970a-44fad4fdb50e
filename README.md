# Bean Software Backend

A Django web application for Bean Software.

## Setup Instructions

1. **Virtual Environment**: A Python virtual environment is already configured in `.venv/`

2. **Install Dependencies**:
   ```bash
   # Django is already installed
   ```

3. **Database Migrations**:
   ```bash
   python manage.py migrate
   ```

4. **Run Development Server**:
   ```bash
   python manage.py runserver 8001
   ```

5. **Access the Application**:
   Open your browser and go to: http://127.0.0.1:8001/

## Project Structure

- `bean_software_backend/` - Main Django project directory
  - `settings.py` - Django settings configuration
  - `urls.py` - URL routing configuration
  - `wsgi.py` - WSGI configuration for deployment
  - `asgi.py` - ASGI configuration for async applications
- `manage.py` - Django management script
- `.venv/` - Python virtual environment

## Next Steps

1. Create Django apps using: `python manage.py startapp <app_name>`
2. Configure your database settings in `settings.py`
3. Create models, views, and templates for your application
4. Set up Django REST Framework if building an API
