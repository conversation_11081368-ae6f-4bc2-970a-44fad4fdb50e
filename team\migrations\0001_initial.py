# Generated by Django 5.2.4 on 2025-07-24 11:45

import django.core.validators
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='TeamMember',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.CharField(max_length=120)),
                ('position', models.CharField(max_length=100)),
                ('bio', models.TextField(blank=True, null=True)),
                ('profile_image', models.URLField(blank=True, help_text="URL to the team member's profile image", max_length=500, null=True, validators=[django.core.validators.URLValidator()])),
                ('email', models.EmailField(blank=True, max_length=190, null=True, validators=[django.core.validators.EmailValidator()])),
                ('linkedin_url', models.URLField(blank=True, help_text='LinkedIn profile URL', max_length=300, null=True, validators=[django.core.validators.URLValidator()])),
                ('github_url', models.URLField(blank=True, help_text='GitHub profile URL', max_length=300, null=True, validators=[django.core.validators.URLValidator()])),
                ('is_active', models.BooleanField(db_index=True, default=True, help_text='Whether this team member should be displayed on the website')),
                ('display_order', models.IntegerField(db_index=True, default=0, help_text='Order in which team members are displayed (lower numbers first)')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Team Member',
                'verbose_name_plural': 'Team Members',
                'db_table': 'team_members',
                'ordering': ['display_order', 'full_name'],
                'indexes': [models.Index(fields=['display_order'], name='idx_display_order'), models.Index(fields=['is_active'], name='idx_is_active')],
            },
        ),
    ]
