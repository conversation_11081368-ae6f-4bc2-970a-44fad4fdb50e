from django.db import models
from django.core.validators import URLValidator, MinValueValidator, MaxValueValidator
from django.utils import timezone


class TechnologyCategory(models.Model):
    """
    Model for organizing technologies into categories.
    Corresponds to the technology_categories table in the database schema.
    """

    name = models.CharField(
        max_length=50,
        unique=True,
        db_index=True,
        help_text="Unique identifier for the category (e.g., 'frontend', 'backend')"
    )
    display_name = models.CharField(
        max_length=100,
        help_text="Human-readable name for the category (e.g., 'Frontend Development')"
    )
    description = models.TextField(
        blank=True, null=True,
        help_text="Description of what technologies belong in this category"
    )
    is_active = models.BooleanField(
        default=True,
        db_index=True,
        help_text="Whether this category should be displayed"
    )
    display_order = models.IntegerField(
        default=0,
        db_index=True,
        help_text="Order in which categories are displayed (lower numbers first)"
    )
    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = 'technology_categories'
        verbose_name = 'Technology Category'
        verbose_name_plural = 'Technology Categories'
        ordering = ['display_order', 'display_name']
        indexes = [
            models.Index(fields=['name'], name='idx_tech_cat_name'),
            models.Index(fields=['is_active'], name='idx_tech_cat_active'),
            models.Index(fields=['display_order'], name='idx_tech_cat_order'),
        ]

    def __str__(self):
        return self.display_name

    @property
    def technology_count(self):
        """Return the number of active technologies in this category."""
        return self.technologies.filter(is_active=True).count()

    def get_featured_technologies(self):
        """Return featured technologies in this category."""
        return self.technologies.filter(is_featured=True, is_active=True).order_by('display_order')


class Technology(models.Model):
    """
    Model for individual technologies with proficiency levels and experience.
    Corresponds to the technologies table in the database schema.
    """

    PROFICIENCY_CHOICES = [
        ('beginner', 'Beginner'),
        ('intermediate', 'Intermediate'),
        ('advanced', 'Advanced'),
        ('expert', 'Expert'),
    ]

    name = models.CharField(max_length=100)
    logo_url = models.URLField(
        max_length=500,
        blank=True, null=True,
        validators=[URLValidator()],
        help_text="URL to the technology logo/icon"
    )
    category = models.ForeignKey(
        TechnologyCategory,
        on_delete=models.CASCADE,
        related_name='technologies',
        db_index=True
    )
    description = models.TextField(
        blank=True, null=True,
        help_text="Description of our experience with this technology"
    )
    proficiency_level = models.CharField(
        max_length=20,
        choices=PROFICIENCY_CHOICES,
        default='intermediate'
    )
    years_experience = models.IntegerField(
        blank=True, null=True,
        validators=[MinValueValidator(0), MaxValueValidator(50)],
        help_text="Number of years of experience with this technology"
    )
    is_featured = models.BooleanField(
        default=False,
        db_index=True,
        help_text="Whether this technology should be featured prominently"
    )
    is_active = models.BooleanField(
        default=True,
        db_index=True,
        help_text="Whether this technology should be displayed"
    )
    display_order = models.IntegerField(
        default=0,
        db_index=True,
        help_text="Order within the category (lower numbers first)"
    )
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'technologies'
        verbose_name = 'Technology'
        verbose_name_plural = 'Technologies'
        ordering = ['category__display_order', 'display_order', 'name']
        indexes = [
            models.Index(fields=['category'], name='idx_tech_category'),
            models.Index(fields=['is_featured'], name='idx_tech_featured'),
            models.Index(fields=['is_active'], name='idx_tech_active'),
            models.Index(fields=['display_order'], name='idx_tech_order'),
        ]

    def __str__(self):
        return f"{self.name} ({self.category.display_name})"

    @property
    def proficiency_percentage(self):
        """Return proficiency as a percentage for progress bars."""
        proficiency_map = {
            'beginner': 25,
            'intermediate': 50,
            'advanced': 75,
            'expert': 100,
        }
        return proficiency_map.get(self.proficiency_level, 50)

    @property
    def experience_level(self):
        """Return a descriptive experience level based on years."""
        if self.years_experience is None:
            return "Some experience"

        if self.years_experience < 1:
            return "New to technology"
        elif self.years_experience < 2:
            return "1+ year experience"
        elif self.years_experience < 5:
            return f"{self.years_experience}+ years experience"
        else:
            return f"{self.years_experience}+ years experience"

    def clean(self):
        """Custom validation for the model."""
        from django.core.exceptions import ValidationError

        # Validate that expert level should have significant experience
        if self.proficiency_level == 'expert' and (not self.years_experience or self.years_experience < 2):
            raise ValidationError(
                "Expert proficiency level should have at least 2 years of experience."
            )
