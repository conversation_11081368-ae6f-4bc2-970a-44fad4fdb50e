from django.contrib import admin
from django.utils.html import format_html
from .models import DemoRequest


@admin.register(DemoRequest)
class DemoRequestAdmin(admin.ModelAdmin):
    """Admin interface for DemoRequest model."""

    list_display = [
        'full_name', 'company_name', 'email', 'project_type',
        'budget_range', 'status', 'is_qualified_lead', 'created_at'
    ]

    list_filter = [
        'status', 'project_type', 'budget_range', 'company_size',
        'timeline', 'how_did_you_hear', 'created_at'
    ]

    search_fields = [
        'first_name', 'last_name', 'email', 'company_name',
        'project_description', 'assigned_to'
    ]

    readonly_fields = ['created_at', 'is_qualified_lead']

    fieldsets = (
        ('Contact Information', {
            'fields': ('first_name', 'last_name', 'email', 'phone')
        }),
        ('Company Information', {
            'fields': ('company_name', 'job_title', 'company_size', 'industry')
        }),
        ('Project Details', {
            'fields': ('project_type', 'budget_range', 'timeline',
                      'project_description', 'specific_requirements')
        }),
        ('Demo Scheduling', {
            'fields': ('preferred_demo_date', 'preferred_demo_time', 'demo_scheduled_at')
        }),
        ('Lead Management', {
            'fields': ('status', 'assigned_to', 'notes')
        }),
        ('Marketing & Analytics', {
            'fields': ('how_did_you_hear', 'is_qualified_lead', 'created_at')
        }),
    )

    date_hierarchy = 'created_at'
    ordering = ['-created_at']

    actions = ['mark_as_contacted', 'mark_as_demo_scheduled']

    def mark_as_contacted(self, request, queryset):
        """Admin action to mark selected requests as contacted."""
        updated = queryset.update(status='contacted')
        self.message_user(request, f'{updated} demo requests marked as contacted.')
    mark_as_contacted.short_description = "Mark selected requests as contacted"

    def mark_as_demo_scheduled(self, request, queryset):
        """Admin action to mark selected requests as demo scheduled."""
        updated = queryset.update(status='demo_scheduled')
        self.message_user(request, f'{updated} demo requests marked as demo scheduled.')
    mark_as_demo_scheduled.short_description = "Mark selected requests as demo scheduled"

    def is_qualified_lead(self, obj):
        """Display qualified lead status with color coding."""
        if obj.is_qualified_lead:
            return format_html('<span style="color: green;">✓ Qualified</span>')
        return format_html('<span style="color: orange;">Standard</span>')
    is_qualified_lead.short_description = 'Lead Quality'
