from django.db import models
from django.core.validators import URLValidator, MinValueValidator, MaxValueValidator
from django.utils import timezone
import json


class AboutUs(models.Model):
    """
    Model for managing About Us page content.
    Corresponds to the about_us table in the database schema.
    """

    title = models.CharField(max_length=200)
    description = models.TextField()
    main_image = models.URLField(max_length=500, blank=True, null=True, validators=[URLValidator()])
    secondary_image = models.URLField(max_length=500, blank=True, null=True, validators=[URLValidator()])
    company_name = models.Char<PERSON>ield(max_length=100)
    established_year = models.IntegerField(
        blank=True, null=True,
        validators=[MinValueValidator(1900), MaxValueValidator(2100)]
    )
    mission_statement = models.TextField(blank=True, null=True)
    vision_statement = models.TextField(blank=True, null=True)
    values = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True, db_index=True)
    display_order = models.IntegerField(default=0, db_index=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'about_us'
        verbose_name = 'About Us'
        verbose_name_plural = 'About Us'
        ordering = ['display_order', '-created_at']
        indexes = [
            models.Index(fields=['is_active'], name='idx_about_active'),
            models.Index(fields=['display_order'], name='idx_about_order'),
        ]

    def __str__(self):
        return f"{self.title} - {self.company_name}"


class Service(models.Model):
    """
    Model for main services offered by the company.
    Corresponds to the services table in the database schema.
    """

    CATEGORY_CHOICES = [
        ('web_development', 'Web Development'),
        ('mobile_development', 'Mobile Development'),
        ('ui_ux_design', 'UI/UX Design'),
        ('consulting', 'Consulting'),
        ('maintenance', 'Maintenance & Support'),
        ('custom_software', 'Custom Software'),
        ('other', 'Other'),
    ]

    title = models.CharField(max_length=150)
    category = models.CharField(
        max_length=50,
        choices=CATEGORY_CHOICES,
        default='other',
        db_index=True,
        help_text="Service category for grouping and filtering"
    )
    short_description = models.CharField(max_length=300, blank=True, null=True)
    full_description = models.TextField(blank=True, null=True)
    icon = models.CharField(max_length=100, blank=True, null=True, help_text="CSS class or icon identifier")
    image = models.URLField(max_length=500, blank=True, null=True, validators=[URLValidator()])
    features = models.JSONField(
        blank=True, null=True,
        help_text="JSON array of service features"
    )
    price_range = models.CharField(max_length=50, blank=True, null=True)
    duration = models.CharField(max_length=50, blank=True, null=True)
    is_featured = models.BooleanField(default=False, db_index=True)
    is_active = models.BooleanField(default=True, db_index=True)
    display_order = models.IntegerField(default=0, db_index=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'services'
        verbose_name = 'Service'
        verbose_name_plural = 'Services'
        ordering = ['display_order', 'title']
        indexes = [
            models.Index(fields=['is_active'], name='idx_services_active'),
            models.Index(fields=['is_featured'], name='idx_services_featured'),
            models.Index(fields=['display_order'], name='idx_services_order'),
            models.Index(fields=['category'], name='idx_services_category'),
        ]

    def __str__(self):
        return self.title

    @property
    def features_list(self):
        """Return features as a Python list."""
        if self.features:
            return self.features if isinstance(self.features, list) else []
        return []


class AdditionalService(models.Model):
    """
    Model for additional/specialized services.
    Corresponds to the additional_services table in the database schema.
    """

    CATEGORY_CHOICES = [
        ('backend', 'Backend Development'),
        ('security', 'Security Services'),
        ('ui_ux', 'UI/UX Design'),
        ('other', 'Other'),
    ]

    title = models.CharField(max_length=150)
    subtitle = models.CharField(max_length=200, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    icon = models.CharField(max_length=100, blank=True, null=True, help_text="CSS class or icon identifier")
    image = models.URLField(max_length=500, blank=True, null=True, validators=[URLValidator()])
    features = models.JSONField(
        blank=True, null=True,
        help_text="JSON array of additional service features"
    )
    category = models.CharField(max_length=50, choices=CATEGORY_CHOICES, db_index=True)
    is_active = models.BooleanField(default=True, db_index=True)
    display_order = models.IntegerField(default=0, db_index=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'additional_services'
        verbose_name = 'Additional Service'
        verbose_name_plural = 'Additional Services'
        ordering = ['category', 'display_order', 'title']
        indexes = [
            models.Index(fields=['category'], name='idx_add_svc_category'),
            models.Index(fields=['is_active'], name='idx_add_svc_active'),
            models.Index(fields=['display_order'], name='idx_add_svc_order'),
        ]

    def __str__(self):
        return f"{self.title} ({self.get_category_display()})"

    @property
    def features_list(self):
        """Return features as a Python list."""
        if self.features:
            return self.features if isinstance(self.features, list) else []
        return []


class FeaturedResource(models.Model):
    """
    Model for content marketing resources (tutorials, guides, case studies).
    Corresponds to the featured_resources table in the database schema.
    """

    RESOURCE_TYPE_CHOICES = [
        ('tutorial', 'Tutorial'),
        ('guide', 'Guide'),
        ('case_study', 'Case Study'),
        ('whitepaper', 'Whitepaper'),
        ('video', 'Video'),
        ('blog_post', 'Blog Post'),
    ]

    DIFFICULTY_CHOICES = [
        ('beginner', 'Beginner'),
        ('intermediate', 'Intermediate'),
        ('advanced', 'Advanced'),
    ]

    title = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    image_url = models.URLField(max_length=500, validators=[URLValidator()])
    resource_type = models.CharField(max_length=50, choices=RESOURCE_TYPE_CHOICES, db_index=True)
    content_url = models.URLField(
        max_length=500,
        validators=[URLValidator()],
        help_text="Link to the full resource content"
    )
    external_link = models.BooleanField(
        default=False,
        help_text="True if links to external site"
    )
    reading_time = models.IntegerField(
        blank=True, null=True,
        help_text="Estimated reading time in minutes",
        validators=[MinValueValidator(1)]
    )
    difficulty_level = models.CharField(
        max_length=20,
        choices=DIFFICULTY_CHOICES,
        blank=True, null=True
    )
    tags = models.JSONField(
        blank=True, null=True,
        help_text="JSON array of tags for categorization"
    )
    author = models.CharField(max_length=100, blank=True, null=True)
    published_date = models.DateField(blank=True, null=True, db_index=True)
    is_featured = models.BooleanField(default=True, db_index=True)
    is_active = models.BooleanField(default=True, db_index=True)
    display_order = models.IntegerField(default=0, db_index=True)
    view_count = models.IntegerField(default=0)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'featured_resources'
        verbose_name = 'Featured Resource'
        verbose_name_plural = 'Featured Resources'
        ordering = ['display_order', '-published_date', 'title']
        indexes = [
            models.Index(fields=['resource_type'], name='idx_resources_type'),
            models.Index(fields=['is_featured'], name='idx_resources_featured'),
            models.Index(fields=['is_active'], name='idx_resources_active'),
            models.Index(fields=['display_order'], name='idx_resources_order'),
            models.Index(fields=['published_date'], name='idx_resources_published'),
        ]

    def __str__(self):
        return f"{self.title} ({self.get_resource_type_display()})"

    @property
    def tags_list(self):
        """Return tags as a Python list."""
        if self.tags:
            return self.tags if isinstance(self.tags, list) else []
        return []

    def increment_view_count(self):
        """Increment the view count for this resource."""
        self.view_count += 1
        self.save(update_fields=['view_count'])


class SocialMediaLink(models.Model):
    """
    Model for managing social media presence.
    Corresponds to the social_media_links table in the database schema.
    """

    PLATFORM_CHOICES = [
        ('linkedin', 'LinkedIn'),
        ('facebook', 'Facebook'),
        ('instagram', 'Instagram'),
        ('twitter', 'Twitter'),
        ('youtube', 'YouTube'),
        ('github', 'GitHub'),
        ('dribbble', 'Dribbble'),
    ]

    platform = models.CharField(
        max_length=50,
        choices=PLATFORM_CHOICES,
        unique=True,
        db_index=True
    )
    display_name = models.CharField(max_length=100)
    url = models.URLField(max_length=500, validators=[URLValidator()])
    icon_class = models.CharField(
        max_length=100,
        blank=True, null=True,
        help_text="CSS class for icon display"
    )
    icon_svg = models.TextField(
        blank=True, null=True,
        help_text="SVG icon code if custom icons used"
    )
    is_active = models.BooleanField(default=True, db_index=True)
    display_order = models.IntegerField(default=0, db_index=True)
    follower_count = models.IntegerField(
        blank=True, null=True,
        help_text="Optional: for displaying follower counts",
        validators=[MinValueValidator(0)]
    )
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'social_media_links'
        verbose_name = 'Social Media Link'
        verbose_name_plural = 'Social Media Links'
        ordering = ['display_order', 'platform']
        indexes = [
            models.Index(fields=['platform'], name='idx_social_platform'),
            models.Index(fields=['is_active'], name='idx_social_active'),
            models.Index(fields=['display_order'], name='idx_social_order'),
        ]

    def __str__(self):
        return f"{self.get_platform_display()} - {self.display_name}"

    @property
    def formatted_follower_count(self):
        """Return formatted follower count (e.g., 1.2K, 5.3M)."""
        if not self.follower_count:
            return None

        if self.follower_count >= 1000000:
            return f"{self.follower_count / 1000000:.1f}M"
        elif self.follower_count >= 1000:
            return f"{self.follower_count / 1000:.1f}K"
        else:
            return str(self.follower_count)
