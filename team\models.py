from django.db import models
from django.core.validators import EmailValidator, URLValidator
from django.utils import timezone


class TeamMember(models.Model):
    """
    Model for managing team member profiles and information.
    Corresponds to the team_members table in the database schema.
    """

    full_name = models.CharField(max_length=120)
    position = models.CharField(max_length=100)
    bio = models.TextField(blank=True, null=True)
    profile_image = models.URLField(
        max_length=500,
        blank=True, null=True,
        validators=[URLValidator()],
        help_text="URL to the team member's profile image"
    )
    email = models.EmailField(
        max_length=190,
        blank=True, null=True,
        validators=[EmailValidator()]
    )
    linkedin_url = models.URLField(
        max_length=300,
        blank=True, null=True,
        validators=[URLValidator()],
        help_text="LinkedIn profile URL"
    )
    github_url = models.URLField(
        max_length=300,
        blank=True, null=True,
        validators=[URLValidator()],
        help_text="GitHub profile URL"
    )
    is_active = models.<PERSON><PERSON>an<PERSON>ield(
        default=True,
        db_index=True,
        help_text="Whether this team member should be displayed on the website"
    )
    display_order = models.IntegerField(
        default=0,
        db_index=True,
        help_text="Order in which team members are displayed (lower numbers first)"
    )
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'team_members'
        verbose_name = 'Team Member'
        verbose_name_plural = 'Team Members'
        ordering = ['display_order', 'full_name']
        indexes = [
            models.Index(fields=['display_order'], name='idx_display_order'),
            models.Index(fields=['is_active'], name='idx_is_active'),
        ]

    def __str__(self):
        return f"{self.full_name} - {self.position}"

    @property
    def first_name(self):
        """Extract first name from full name."""
        return self.full_name.split()[0] if self.full_name else ""

    @property
    def last_name(self):
        """Extract last name from full name."""
        name_parts = self.full_name.split()
        return " ".join(name_parts[1:]) if len(name_parts) > 1 else ""

    @property
    def has_social_links(self):
        """Check if the team member has any social media links."""
        return bool(self.linkedin_url or self.github_url)

    @property
    def initials(self):
        """Get initials from the full name."""
        name_parts = self.full_name.split()
        if len(name_parts) >= 2:
            return f"{name_parts[0][0]}{name_parts[-1][0]}".upper()
        elif len(name_parts) == 1:
            return name_parts[0][:2].upper()
        return "TM"

    def get_social_links(self):
        """Return a dictionary of available social links."""
        links = {}
        if self.linkedin_url:
            links['linkedin'] = self.linkedin_url
        if self.github_url:
            links['github'] = self.github_url
        return links

    def clean(self):
        """Custom validation for the model."""
        from django.core.exceptions import ValidationError

        # Ensure at least one of the social links is provided if email is not provided
        if not self.email and not self.linkedin_url and not self.github_url:
            raise ValidationError(
                "At least one contact method (email, LinkedIn, or GitHub) must be provided."
            )
