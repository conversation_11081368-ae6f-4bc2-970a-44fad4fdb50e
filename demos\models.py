from django.db import models
from django.core.validators import EmailValidator, RegexValidator
from django.utils import timezone


class DemoRequest(models.Model):
    """
    Model for handling demo request submissions and lead management.
    Corresponds to the demo_requests table in the database schema.
    """

    # Company size choices
    COMPANY_SIZE_CHOICES = [
        ('1-10', '1-10 employees'),
        ('11-50', '11-50 employees'),
        ('51-200', '51-200 employees'),
        ('201-1000', '201-1000 employees'),
        ('1000+', '1000+ employees'),
    ]

    # Project type choices
    PROJECT_TYPE_CHOICES = [
        ('web_app', 'Web Application'),
        ('mobile_app', 'Mobile Application'),
        ('api', 'API Development'),
        ('custom_software', 'Custom Software'),
        ('other', 'Other'),
    ]

    # Budget range choices
    BUDGET_RANGE_CHOICES = [
        ('under_10k', 'Under $10,000'),
        ('10k_25k', '$10,000 - $25,000'),
        ('25k_50k', '$25,000 - $50,000'),
        ('50k_100k', '$50,000 - $100,000'),
        ('100k_plus', '$100,000+'),
    ]

    # Timeline choices
    TIMELINE_CHOICES = [
        ('asap', 'ASAP'),
        ('1_month', '1 Month'),
        ('3_months', '3 Months'),
        ('6_months', '6 Months'),
        ('1_year_plus', '1 Year+'),
    ]

    # Demo time preferences
    DEMO_TIME_CHOICES = [
        ('morning', 'Morning'),
        ('afternoon', 'Afternoon'),
        ('evening', 'Evening'),
    ]

    # How did you hear choices
    HEAR_ABOUT_CHOICES = [
        ('google', 'Google Search'),
        ('social_media', 'Social Media'),
        ('referral', 'Referral'),
        ('event', 'Event'),
        ('other', 'Other'),
    ]

    # Status choices
    STATUS_CHOICES = [
        ('new', 'New'),
        ('contacted', 'Contacted'),
        ('demo_scheduled', 'Demo Scheduled'),
        ('demo_completed', 'Demo Completed'),
        ('proposal_sent', 'Proposal Sent'),
        ('closed_won', 'Closed Won'),
        ('closed_lost', 'Closed Lost'),
    ]

    # Phone number validator
    phone_validator = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed."
    )

    # Basic contact information
    first_name = models.CharField(max_length=120)
    last_name = models.CharField(max_length=120)
    email = models.EmailField(max_length=190, validators=[EmailValidator()])
    phone = models.CharField(max_length=30, blank=True, null=True, validators=[phone_validator])

    # Company information
    company_name = models.CharField(max_length=150)
    job_title = models.CharField(max_length=100, blank=True, null=True)
    company_size = models.CharField(max_length=50, choices=COMPANY_SIZE_CHOICES, blank=True, null=True)
    industry = models.CharField(max_length=100, blank=True, null=True)

    # Project details
    project_type = models.CharField(max_length=100, choices=PROJECT_TYPE_CHOICES, blank=True, null=True)
    budget_range = models.CharField(max_length=50, choices=BUDGET_RANGE_CHOICES, blank=True, null=True)
    timeline = models.CharField(max_length=50, choices=TIMELINE_CHOICES, blank=True, null=True)
    project_description = models.TextField()
    specific_requirements = models.TextField(blank=True, null=True)

    # Demo scheduling
    preferred_demo_date = models.DateField(blank=True, null=True)
    preferred_demo_time = models.CharField(max_length=20, choices=DEMO_TIME_CHOICES, blank=True, null=True)

    # Marketing attribution
    how_did_you_hear = models.CharField(max_length=100, choices=HEAR_ABOUT_CHOICES, blank=True, null=True)

    # Lead management
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='new', db_index=True)
    assigned_to = models.CharField(max_length=100, blank=True, null=True)
    demo_scheduled_at = models.DateTimeField(blank=True, null=True)
    notes = models.TextField(blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(default=timezone.now, db_index=True)

    class Meta:
        db_table = 'demo_requests'
        verbose_name = 'Demo Request'
        verbose_name_plural = 'Demo Requests'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['email', 'created_at'], name='idx_demo_email_created_at'),
            models.Index(fields=['status'], name='idx_demo_status'),
            models.Index(fields=['preferred_demo_date'], name='idx_demo_date'),
            models.Index(fields=['budget_range'], name='idx_demo_budget'),
            models.Index(fields=['project_type'], name='idx_demo_project_type'),
        ]

    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.company_name} ({self.get_status_display()})"

    @property
    def full_name(self):
        """Return the full name of the contact."""
        return f"{self.first_name} {self.last_name}"

    @property
    def is_qualified_lead(self):
        """Check if this is a qualified lead based on budget and company size."""
        qualified_budgets = ['25k_50k', '50k_100k', '100k_plus']
        qualified_sizes = ['51-200', '201-1000', '1000+']
        return (self.budget_range in qualified_budgets or
                self.company_size in qualified_sizes)

    def mark_as_contacted(self, assigned_to=None):
        """Mark the demo request as contacted."""
        self.status = 'contacted'
        if assigned_to:
            self.assigned_to = assigned_to
        self.save()

    def schedule_demo(self, demo_datetime):
        """Schedule a demo for this request."""
        self.status = 'demo_scheduled'
        self.demo_scheduled_at = demo_datetime
        self.save()

    def complete_demo(self):
        """Mark the demo as completed."""
        self.status = 'demo_completed'
        self.save()
