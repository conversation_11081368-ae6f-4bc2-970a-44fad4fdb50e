from django.test import TestCase
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import datetime, date
from .models import DemoRequest


class DemoRequestModelTest(TestCase):
    """Test cases for the DemoRequest model."""

    def setUp(self):
        """Set up test data."""
        self.valid_demo_data = {
            'first_name': '<PERSON>',
            'last_name': '<PERSON><PERSON>',
            'email': '<EMAIL>',
            'phone': '+1234567890',
            'company_name': 'Test Company',
            'job_title': 'CTO',
            'company_size': '51-200',
            'industry': 'Technology',
            'project_type': 'web_app',
            'budget_range': '50k_100k',
            'timeline': '3_months',
            'project_description': 'We need a web application for our business.',
            'specific_requirements': 'Must be mobile responsive',
            'preferred_demo_date': date.today(),
            'preferred_demo_time': 'morning',
            'how_did_you_hear': 'google',
        }

    def test_create_demo_request_with_valid_data(self):
        """Test creating a demo request with valid data."""
        demo = DemoRequest.objects.create(**self.valid_demo_data)
        self.assertEqual(demo.first_name, 'John')
        self.assertEqual(demo.last_name, 'Doe')
        self.assertEqual(demo.email, '<EMAIL>')
        self.assertEqual(demo.company_name, 'Test Company')
        self.assertEqual(demo.status, 'new')  # Default status
        self.assertTrue(demo.created_at)

    def test_demo_request_str_representation(self):
        """Test the string representation of DemoRequest."""
        demo = DemoRequest.objects.create(**self.valid_demo_data)
        expected_str = "John Doe - Test Company (New)"
        self.assertEqual(str(demo), expected_str)

    def test_full_name_property(self):
        """Test the full_name property."""
        demo = DemoRequest.objects.create(**self.valid_demo_data)
        self.assertEqual(demo.full_name, "John Doe")

    def test_is_qualified_lead_property(self):
        """Test the is_qualified_lead property."""
        # Test qualified by budget
        demo = DemoRequest.objects.create(**self.valid_demo_data)
        self.assertTrue(demo.is_qualified_lead)

        # Test qualified by company size
        demo.budget_range = 'under_10k'
        demo.company_size = '1000+'
        demo.save()
        self.assertTrue(demo.is_qualified_lead)

        # Test not qualified
        demo.budget_range = 'under_10k'
        demo.company_size = '1-10'
        demo.save()
        self.assertFalse(demo.is_qualified_lead)

    def test_mark_as_contacted_method(self):
        """Test the mark_as_contacted method."""
        demo = DemoRequest.objects.create(**self.valid_demo_data)
        demo.mark_as_contacted('<EMAIL>')

        demo.refresh_from_db()
        self.assertEqual(demo.status, 'contacted')
        self.assertEqual(demo.assigned_to, '<EMAIL>')

    def test_schedule_demo_method(self):
        """Test the schedule_demo method."""
        demo = DemoRequest.objects.create(**self.valid_demo_data)
        demo_time = timezone.now()
        demo.schedule_demo(demo_time)

        demo.refresh_from_db()
        self.assertEqual(demo.status, 'demo_scheduled')
        self.assertEqual(demo.demo_scheduled_at, demo_time)

    def test_complete_demo_method(self):
        """Test the complete_demo method."""
        demo = DemoRequest.objects.create(**self.valid_demo_data)
        demo.complete_demo()

        demo.refresh_from_db()
        self.assertEqual(demo.status, 'demo_completed')

    def test_email_validation(self):
        """Test email field validation."""
        invalid_data = self.valid_demo_data.copy()
        invalid_data['email'] = 'invalid-email'

        demo = DemoRequest(**invalid_data)
        with self.assertRaises(ValidationError):
            demo.full_clean()

    def test_phone_validation(self):
        """Test phone field validation."""
        invalid_data = self.valid_demo_data.copy()
        invalid_data['phone'] = 'invalid-phone'

        demo = DemoRequest(**invalid_data)
        with self.assertRaises(ValidationError):
            demo.full_clean()

    def test_required_fields(self):
        """Test that required fields are enforced."""
        # Test missing first_name
        invalid_data = self.valid_demo_data.copy()
        del invalid_data['first_name']

        demo = DemoRequest(**invalid_data)
        with self.assertRaises(ValidationError):
            demo.full_clean()

    def test_choice_field_validation(self):
        """Test that choice fields only accept valid choices."""
        invalid_data = self.valid_demo_data.copy()
        invalid_data['status'] = 'invalid_status'

        demo = DemoRequest(**invalid_data)
        with self.assertRaises(ValidationError):
            demo.full_clean()

    def test_ordering(self):
        """Test the default ordering of demo requests."""
        # Create multiple demo requests
        demo1 = DemoRequest.objects.create(**self.valid_demo_data)

        # Create second demo request with different data
        data2 = self.valid_demo_data.copy()
        data2['email'] = '<EMAIL>'
        data2['first_name'] = 'Jane'
        demo2 = DemoRequest.objects.create(**data2)

        # Check ordering (should be by -created_at)
        demos = list(DemoRequest.objects.all())
        self.assertEqual(demos[0], demo2)  # Most recent first
        self.assertEqual(demos[1], demo1)
