from django.test import TestCase
from django.core.exceptions import ValidationError
from .models import TechnologyCategory, Technology


class TechnologyCategoryModelTest(TestCase):
    """Test cases for the TechnologyCategory model."""

    def setUp(self):
        """Set up test data."""
        self.valid_category_data = {
            'name': 'frontend',
            'display_name': 'Frontend Development',
            'description': 'Technologies for building user interfaces.',
        }

    def test_create_technology_category_with_valid_data(self):
        """Test creating a TechnologyCategory instance with valid data."""
        category = TechnologyCategory.objects.create(**self.valid_category_data)
        self.assertEqual(category.name, 'frontend')
        self.assertEqual(category.display_name, 'Frontend Development')
        self.assertTrue(category.is_active)  # Default value
        self.assertEqual(category.display_order, 0)  # Default value

    def test_technology_category_str_representation(self):
        """Test the string representation of TechnologyCategory."""
        category = TechnologyCategory.objects.create(**self.valid_category_data)
        self.assertEqual(str(category), 'Frontend Development')

    def test_technology_count_property(self):
        """Test the technology_count property."""
        category = TechnologyCategory.objects.create(**self.valid_category_data)

        # Initially should be 0
        self.assertEqual(category.technology_count, 0)

        # Add a technology
        Technology.objects.create(
            name='React',
            category=category,
            proficiency_level='advanced'
        )

        # Should now be 1
        self.assertEqual(category.technology_count, 1)

        # Add an inactive technology
        Technology.objects.create(
            name='Angular',
            category=category,
            proficiency_level='intermediate',
            is_active=False
        )

        # Should still be 1 (only active technologies counted)
        self.assertEqual(category.technology_count, 1)

    def test_get_featured_technologies_method(self):
        """Test the get_featured_technologies method."""
        category = TechnologyCategory.objects.create(**self.valid_category_data)

        # Create featured and non-featured technologies
        tech1 = Technology.objects.create(
            name='React',
            category=category,
            proficiency_level='advanced',
            is_featured=True,
            display_order=1
        )

        tech2 = Technology.objects.create(
            name='Vue.js',
            category=category,
            proficiency_level='intermediate',
            is_featured=False
        )

        tech3 = Technology.objects.create(
            name='Angular',
            category=category,
            proficiency_level='intermediate',
            is_featured=True,
            display_order=2
        )

        featured_techs = list(category.get_featured_technologies())
        self.assertEqual(len(featured_techs), 2)
        self.assertIn(tech1, featured_techs)
        self.assertIn(tech3, featured_techs)
        self.assertNotIn(tech2, featured_techs)

        # Check ordering by display_order
        self.assertEqual(featured_techs[0], tech1)
        self.assertEqual(featured_techs[1], tech3)

    def test_name_uniqueness(self):
        """Test that name field is unique."""
        TechnologyCategory.objects.create(**self.valid_category_data)

        # Try to create another with the same name
        duplicate_data = self.valid_category_data.copy()
        duplicate_data['display_name'] = 'Another Frontend'

        with self.assertRaises(Exception):  # Should raise IntegrityError
            TechnologyCategory.objects.create(**duplicate_data)


class TechnologyModelTest(TestCase):
    """Test cases for the Technology model."""

    def setUp(self):
        """Set up test data."""
        self.category = TechnologyCategory.objects.create(
            name='frontend',
            display_name='Frontend Development'
        )

        self.valid_technology_data = {
            'name': 'React',
            'category': self.category,
            'description': 'A JavaScript library for building user interfaces.',
            'proficiency_level': 'advanced',
            'years_experience': 3,
        }

    def test_create_technology_with_valid_data(self):
        """Test creating a Technology instance with valid data."""
        tech = Technology.objects.create(**self.valid_technology_data)
        self.assertEqual(tech.name, 'React')
        self.assertEqual(tech.category, self.category)
        self.assertEqual(tech.proficiency_level, 'advanced')
        self.assertFalse(tech.is_featured)  # Default value
        self.assertTrue(tech.is_active)  # Default value

    def test_technology_str_representation(self):
        """Test the string representation of Technology."""
        tech = Technology.objects.create(**self.valid_technology_data)
        expected_str = "React (Frontend Development)"
        self.assertEqual(str(tech), expected_str)

    def test_proficiency_percentage_property(self):
        """Test the proficiency_percentage property."""
        tech = Technology.objects.create(**self.valid_technology_data)

        # Test different proficiency levels
        tech.proficiency_level = 'beginner'
        self.assertEqual(tech.proficiency_percentage, 25)

        tech.proficiency_level = 'intermediate'
        self.assertEqual(tech.proficiency_percentage, 50)

        tech.proficiency_level = 'advanced'
        self.assertEqual(tech.proficiency_percentage, 75)

        tech.proficiency_level = 'expert'
        self.assertEqual(tech.proficiency_percentage, 100)

    def test_experience_level_property(self):
        """Test the experience_level property."""
        tech = Technology.objects.create(**self.valid_technology_data)

        # Test different experience levels
        tech.years_experience = None
        self.assertEqual(tech.experience_level, "Some experience")

        tech.years_experience = 0
        self.assertEqual(tech.experience_level, "New to technology")

        tech.years_experience = 1
        self.assertEqual(tech.experience_level, "1+ year experience")

        tech.years_experience = 3
        self.assertEqual(tech.experience_level, "3+ years experience")

        tech.years_experience = 7
        self.assertEqual(tech.experience_level, "7+ years experience")

    def test_clean_method_validation(self):
        """Test the clean method validation."""
        # Test expert level with insufficient experience
        invalid_data = self.valid_technology_data.copy()
        invalid_data['proficiency_level'] = 'expert'
        invalid_data['years_experience'] = 1

        tech = Technology(**invalid_data)
        with self.assertRaises(ValidationError):
            tech.clean()

        # Test expert level with sufficient experience (should pass)
        valid_data = invalid_data.copy()
        valid_data['years_experience'] = 3
        tech = Technology(**valid_data)
        try:
            tech.clean()  # Should not raise an exception
        except ValidationError:
            self.fail("clean() raised ValidationError unexpectedly!")

    def test_foreign_key_relationship(self):
        """Test the foreign key relationship with TechnologyCategory."""
        tech = Technology.objects.create(**self.valid_technology_data)

        # Test accessing category from technology
        self.assertEqual(tech.category.name, 'frontend')

        # Test accessing technologies from category
        self.assertIn(tech, self.category.technologies.all())

    def test_cascade_delete(self):
        """Test that technologies are deleted when category is deleted."""
        tech = Technology.objects.create(**self.valid_technology_data)
        tech_id = tech.id

        # Delete the category
        self.category.delete()

        # Technology should also be deleted
        with self.assertRaises(Technology.DoesNotExist):
            Technology.objects.get(id=tech_id)
