from django.test import TestCase
from django.core.exceptions import ValidationError
from .models import TeamMember


class TeamMemberModelTest(TestCase):
    """Test cases for the TeamMember model."""

    def setUp(self):
        """Set up test data."""
        self.valid_team_data = {
            'full_name': '<PERSON>',
            'position': 'Senior Developer',
            'bio': 'Experienced full-stack developer with 5+ years of experience.',
            'email': '<EMAIL>',
            'linkedin_url': 'https://linkedin.com/in/johnsmith',
            'github_url': 'https://github.com/johnsmith',
        }

    def test_create_team_member_with_valid_data(self):
        """Test creating a TeamMember instance with valid data."""
        member = TeamMember.objects.create(**self.valid_team_data)
        self.assertEqual(member.full_name, '<PERSON>')
        self.assertEqual(member.position, 'Senior Developer')
        self.assertTrue(member.is_active)  # Default value
        self.assertEqual(member.display_order, 0)  # Default value

    def test_team_member_str_representation(self):
        """Test the string representation of TeamMember."""
        member = TeamMember.objects.create(**self.valid_team_data)
        expected_str = "<PERSON> - Senior Developer"
        self.assertEqual(str(member), expected_str)

    def test_first_name_property(self):
        """Test the first_name property."""
        member = TeamMember.objects.create(**self.valid_team_data)
        self.assertEqual(member.first_name, "John")

    def test_last_name_property(self):
        """Test the last_name property."""
        member = TeamMember.objects.create(**self.valid_team_data)
        self.assertEqual(member.last_name, "Smith")

        # Test with multiple last names
        member.full_name = "John Michael Smith Jr."
        self.assertEqual(member.last_name, "Michael Smith Jr.")

    def test_has_social_links_property(self):
        """Test the has_social_links property."""
        member = TeamMember.objects.create(**self.valid_team_data)
        self.assertTrue(member.has_social_links)

        # Test with no social links
        member.linkedin_url = None
        member.github_url = None
        self.assertFalse(member.has_social_links)

    def test_initials_property(self):
        """Test the initials property."""
        member = TeamMember.objects.create(**self.valid_team_data)
        self.assertEqual(member.initials, "JS")

        # Test with single name
        member.full_name = "John"
        self.assertEqual(member.initials, "JO")

        # Test with empty name
        member.full_name = ""
        self.assertEqual(member.initials, "TM")

    def test_get_social_links_method(self):
        """Test the get_social_links method."""
        member = TeamMember.objects.create(**self.valid_team_data)
        social_links = member.get_social_links()

        expected_links = {
            'linkedin': 'https://linkedin.com/in/johnsmith',
            'github': 'https://github.com/johnsmith'
        }
        self.assertEqual(social_links, expected_links)

        # Test with only one social link
        member.github_url = None
        social_links = member.get_social_links()
        expected_links = {'linkedin': 'https://linkedin.com/in/johnsmith'}
        self.assertEqual(social_links, expected_links)

    def test_clean_method_validation(self):
        """Test the clean method validation."""
        # Test with no contact methods
        invalid_data = self.valid_team_data.copy()
        invalid_data['email'] = None
        invalid_data['linkedin_url'] = None
        invalid_data['github_url'] = None

        member = TeamMember(**invalid_data)
        with self.assertRaises(ValidationError):
            member.clean()

        # Test with at least one contact method (should pass)
        valid_data = invalid_data.copy()
        valid_data['email'] = '<EMAIL>'
        member = TeamMember(**valid_data)
        try:
            member.clean()  # Should not raise an exception
        except ValidationError:
            self.fail("clean() raised ValidationError unexpectedly!")

    def test_url_validation(self):
        """Test URL field validation."""
        invalid_data = self.valid_team_data.copy()
        invalid_data['linkedin_url'] = 'invalid-url'

        member = TeamMember(**invalid_data)
        with self.assertRaises(ValidationError):
            member.full_clean()

    def test_email_validation(self):
        """Test email field validation."""
        invalid_data = self.valid_team_data.copy()
        invalid_data['email'] = 'invalid-email'

        member = TeamMember(**invalid_data)
        with self.assertRaises(ValidationError):
            member.full_clean()

    def test_ordering(self):
        """Test the default ordering of team members."""
        # Create multiple team members with different display orders
        member1 = TeamMember.objects.create(
            full_name='Alice Johnson',
            position='Designer',
            display_order=2,
            email='<EMAIL>'
        )

        member2 = TeamMember.objects.create(
            full_name='Bob Wilson',
            position='Developer',
            display_order=1,
            email='<EMAIL>'
        )

        # Check ordering (should be by display_order, then full_name)
        members = list(TeamMember.objects.all())
        self.assertEqual(members[0], member2)  # Lower display_order first
        self.assertEqual(members[1], member1)
