"""
Base models and utilities for multi-language support.

This module contains abstract base classes and utilities that are shared
across all Turkish models to ensure consistent behavior and database routing.
"""

from django.db import models
from django.utils import timezone


class TurkishBaseModel(models.Model):
    """
    Abstract base model for all Turkish content models.

    This ensures that all Turkish models are automatically routed to the
    Turkish database and have consistent metadata.
    """

    class Meta:
        abstract = True
        # This will be overridden in concrete models, but provides a default
        app_label = 'turkish_content'

    def save(self, *args, **kwargs):
        """Override save to ensure Turkish models use the Turkish database."""
        # Force using the Turkish database
        kwargs.setdefault('using', 'turkish')
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        """Override delete to ensure Turkish models use the Turkish database."""
        # Force using the Turkish database
        kwargs.setdefault('using', 'turkish')
        super().delete(*args, **kwargs)

    @classmethod
    def get_database_alias(cls):
        """Return the database alias for this model."""
        return 'turkish'


class TimestampedTurkishModel(TurkishBaseModel):
    """
    Abstract base model that includes created_at and updated_at fields
    for Turkish models.
    """

    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True





class TurkishModelManager(models.Manager):
    """
    Custom manager for Turkish models that ensures proper database routing.
    """

    def get_queryset(self):
        """Return queryset using the Turkish database."""
        return super().get_queryset().using('turkish')

    def create(self, **kwargs):
        """Create object in the Turkish database."""
        return super().using('turkish').create(**kwargs)

    def bulk_create(self, objs, **kwargs):
        """Bulk create objects in the Turkish database."""
        return super().using('turkish').bulk_create(objs, **kwargs)

    def get_or_create(self, **kwargs):
        """Get or create object in the Turkish database."""
        return super().using('turkish').get_or_create(**kwargs)

    def update_or_create(self, **kwargs):
        """Update or create object in the Turkish database."""
        return super().using('turkish').update_or_create(**kwargs)




# Utility functions for working with multi-language models



