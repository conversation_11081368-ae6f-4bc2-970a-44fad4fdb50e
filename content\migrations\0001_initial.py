# Generated by Django 5.2.4 on 2025-07-24 11:45

import django.core.validators
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AboutUs',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('main_image', models.URLField(blank=True, max_length=500, null=True, validators=[django.core.validators.URLValidator()])),
                ('secondary_image', models.URLField(blank=True, max_length=500, null=True, validators=[django.core.validators.URLValidator()])),
                ('company_name', models.CharField(max_length=100)),
                ('established_year', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1900), django.core.validators.MaxValueValidator(2100)])),
                ('mission_statement', models.TextField(blank=True, null=True)),
                ('vision_statement', models.TextField(blank=True, null=True)),
                ('values', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(db_index=True, default=True)),
                ('display_order', models.IntegerField(db_index=True, default=0)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'About Us',
                'verbose_name_plural': 'About Us',
                'db_table': 'about_us',
                'ordering': ['display_order', '-created_at'],
                'indexes': [models.Index(fields=['is_active'], name='idx_about_active'), models.Index(fields=['display_order'], name='idx_about_order')],
            },
        ),
        migrations.CreateModel(
            name='AdditionalService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=150)),
                ('subtitle', models.CharField(blank=True, max_length=200, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('icon', models.CharField(blank=True, help_text='CSS class or icon identifier', max_length=100, null=True)),
                ('image', models.URLField(blank=True, max_length=500, null=True, validators=[django.core.validators.URLValidator()])),
                ('features', models.JSONField(blank=True, help_text='JSON array of additional service features', null=True)),
                ('category', models.CharField(choices=[('backend', 'Backend Development'), ('security', 'Security Services'), ('ui_ux', 'UI/UX Design'), ('other', 'Other')], db_index=True, max_length=50)),
                ('is_active', models.BooleanField(db_index=True, default=True)),
                ('display_order', models.IntegerField(db_index=True, default=0)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Additional Service',
                'verbose_name_plural': 'Additional Services',
                'db_table': 'additional_services',
                'ordering': ['category', 'display_order', 'title'],
                'indexes': [models.Index(fields=['category'], name='idx_add_svc_category'), models.Index(fields=['is_active'], name='idx_add_svc_active'), models.Index(fields=['display_order'], name='idx_add_svc_order')],
            },
        ),
        migrations.CreateModel(
            name='FeaturedResource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True, null=True)),
                ('image_url', models.URLField(max_length=500, validators=[django.core.validators.URLValidator()])),
                ('resource_type', models.CharField(choices=[('tutorial', 'Tutorial'), ('guide', 'Guide'), ('case_study', 'Case Study'), ('whitepaper', 'Whitepaper'), ('video', 'Video'), ('blog_post', 'Blog Post')], db_index=True, max_length=50)),
                ('content_url', models.URLField(help_text='Link to the full resource content', max_length=500, validators=[django.core.validators.URLValidator()])),
                ('external_link', models.BooleanField(default=False, help_text='True if links to external site')),
                ('reading_time', models.IntegerField(blank=True, help_text='Estimated reading time in minutes', null=True, validators=[django.core.validators.MinValueValidator(1)])),
                ('difficulty_level', models.CharField(blank=True, choices=[('beginner', 'Beginner'), ('intermediate', 'Intermediate'), ('advanced', 'Advanced')], max_length=20, null=True)),
                ('tags', models.JSONField(blank=True, help_text='JSON array of tags for categorization', null=True)),
                ('author', models.CharField(blank=True, max_length=100, null=True)),
                ('published_date', models.DateField(blank=True, db_index=True, null=True)),
                ('is_featured', models.BooleanField(db_index=True, default=True)),
                ('is_active', models.BooleanField(db_index=True, default=True)),
                ('display_order', models.IntegerField(db_index=True, default=0)),
                ('view_count', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Featured Resource',
                'verbose_name_plural': 'Featured Resources',
                'db_table': 'featured_resources',
                'ordering': ['display_order', '-published_date', 'title'],
                'indexes': [models.Index(fields=['resource_type'], name='idx_resources_type'), models.Index(fields=['is_featured'], name='idx_resources_featured'), models.Index(fields=['is_active'], name='idx_resources_active'), models.Index(fields=['display_order'], name='idx_resources_order'), models.Index(fields=['published_date'], name='idx_resources_published')],
            },
        ),
        migrations.CreateModel(
            name='Service',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=150)),
                ('short_description', models.CharField(blank=True, max_length=300, null=True)),
                ('full_description', models.TextField(blank=True, null=True)),
                ('icon', models.CharField(blank=True, help_text='CSS class or icon identifier', max_length=100, null=True)),
                ('image', models.URLField(blank=True, max_length=500, null=True, validators=[django.core.validators.URLValidator()])),
                ('features', models.JSONField(blank=True, help_text='JSON array of service features', null=True)),
                ('price_range', models.CharField(blank=True, max_length=50, null=True)),
                ('duration', models.CharField(blank=True, max_length=50, null=True)),
                ('is_featured', models.BooleanField(db_index=True, default=False)),
                ('is_active', models.BooleanField(db_index=True, default=True)),
                ('display_order', models.IntegerField(db_index=True, default=0)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Service',
                'verbose_name_plural': 'Services',
                'db_table': 'services',
                'ordering': ['display_order', 'title'],
                'indexes': [models.Index(fields=['is_active'], name='idx_services_active'), models.Index(fields=['is_featured'], name='idx_services_featured'), models.Index(fields=['display_order'], name='idx_services_order')],
            },
        ),
        migrations.CreateModel(
            name='SocialMediaLink',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('platform', models.CharField(choices=[('linkedin', 'LinkedIn'), ('facebook', 'Facebook'), ('instagram', 'Instagram'), ('twitter', 'Twitter'), ('youtube', 'YouTube'), ('github', 'GitHub'), ('dribbble', 'Dribbble')], db_index=True, max_length=50, unique=True)),
                ('display_name', models.CharField(max_length=100)),
                ('url', models.URLField(max_length=500, validators=[django.core.validators.URLValidator()])),
                ('icon_class', models.CharField(blank=True, help_text='CSS class for icon display', max_length=100, null=True)),
                ('icon_svg', models.TextField(blank=True, help_text='SVG icon code if custom icons used', null=True)),
                ('is_active', models.BooleanField(db_index=True, default=True)),
                ('display_order', models.IntegerField(db_index=True, default=0)),
                ('follower_count', models.IntegerField(blank=True, help_text='Optional: for displaying follower counts', null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Social Media Link',
                'verbose_name_plural': 'Social Media Links',
                'db_table': 'social_media_links',
                'ordering': ['display_order', 'platform'],
                'indexes': [models.Index(fields=['platform'], name='idx_social_platform'), models.Index(fields=['is_active'], name='idx_social_active'), models.Index(fields=['display_order'], name='idx_social_order')],
            },
        ),
    ]
