from django.contrib import admin
from django.utils.html import format_html
from .models import TechnologyCategory, Technology


class TechnologyInline(admin.TabularInline):
    """Inline admin for technologies within a category."""
    model = Technology
    extra = 0
    fields = ['name', 'proficiency_level', 'years_experience', 'is_featured', 'is_active', 'display_order']
    ordering = ['display_order', 'name']


@admin.register(TechnologyCategory)
class TechnologyCategoryAdmin(admin.ModelAdmin):
    """Admin interface for TechnologyCategory model."""

    list_display = ['display_name', 'name', 'technology_count', 'is_active', 'display_order', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'display_name', 'description']
    readonly_fields = ['created_at', 'technology_count']
    ordering = ['display_order', 'display_name']
    inlines = [TechnologyInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'display_name', 'description')
        }),
        ('Display Settings', {
            'fields': ('is_active', 'display_order')
        }),
        ('Statistics', {
            'fields': ('technology_count',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )

    def technology_count(self, obj):
        """Display the number of technologies in this category."""
        count = obj.technology_count
        if count > 0:
            return format_html('<span style="color: green;">{} technologies</span>', count)
        return format_html('<span style="color: orange;">No technologies</span>')
    technology_count.short_description = 'Technologies'


@admin.register(Technology)
class TechnologyAdmin(admin.ModelAdmin):
    """Admin interface for Technology model."""

    list_display = [
        'name', 'category', 'proficiency_level', 'years_experience',
        'proficiency_percentage', 'is_featured', 'is_active', 'display_order'
    ]
    list_filter = [
        'category', 'proficiency_level', 'is_featured',
        'is_active', 'years_experience', 'created_at'
    ]
    search_fields = ['name', 'description', 'category__name', 'category__display_name']
    readonly_fields = ['created_at', 'updated_at', 'proficiency_percentage', 'experience_level']
    ordering = ['category__display_order', 'display_order', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'category', 'description', 'logo_url')
        }),
        ('Proficiency & Experience', {
            'fields': ('proficiency_level', 'years_experience', 'proficiency_percentage', 'experience_level')
        }),
        ('Display Settings', {
            'fields': ('is_featured', 'is_active', 'display_order')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_as_featured', 'mark_as_not_featured', 'set_intermediate_proficiency']

    def proficiency_percentage(self, obj):
        """Display proficiency as a visual percentage."""
        percentage = obj.proficiency_percentage
        color = 'red' if percentage < 50 else 'orange' if percentage < 75 else 'green'
        return format_html(
            '<div style="width: 100px; background-color: #f0f0f0; border-radius: 3px;">'
            '<div style="width: {}%; background-color: {}; height: 20px; border-radius: 3px; '
            'text-align: center; color: white; font-size: 12px; line-height: 20px;">'
            '{}%</div></div>',
            percentage, color, percentage
        )
    proficiency_percentage.short_description = 'Proficiency'

    def mark_as_featured(self, request, queryset):
        """Admin action to mark selected technologies as featured."""
        updated = queryset.update(is_featured=True)
        self.message_user(request, f'{updated} technologies marked as featured.')
    mark_as_featured.short_description = "Mark selected technologies as featured"

    def mark_as_not_featured(self, request, queryset):
        """Admin action to mark selected technologies as not featured."""
        updated = queryset.update(is_featured=False)
        self.message_user(request, f'{updated} technologies marked as not featured.')
    mark_as_not_featured.short_description = "Mark selected technologies as not featured"

    def set_intermediate_proficiency(self, request, queryset):
        """Admin action to set proficiency to intermediate."""
        updated = queryset.update(proficiency_level='intermediate')
        self.message_user(request, f'{updated} technologies set to intermediate proficiency.')
    set_intermediate_proficiency.short_description = "Set proficiency to intermediate"
