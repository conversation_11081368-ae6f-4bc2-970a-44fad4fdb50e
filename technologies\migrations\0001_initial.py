# Generated by Django 5.2.4 on 2025-07-24 11:45

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='TechnologyCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, help_text="Unique identifier for the category (e.g., 'frontend', 'backend')", max_length=50, unique=True)),
                ('display_name', models.CharField(help_text="Human-readable name for the category (e.g., 'Frontend Development')", max_length=100)),
                ('description', models.TextField(blank=True, help_text='Description of what technologies belong in this category', null=True)),
                ('is_active', models.<PERSON><PERSON>an<PERSON>ield(db_index=True, default=True, help_text='Whether this category should be displayed')),
                ('display_order', models.IntegerField(db_index=True, default=0, help_text='Order in which categories are displayed (lower numbers first)')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'verbose_name': 'Technology Category',
                'verbose_name_plural': 'Technology Categories',
                'db_table': 'technology_categories',
                'ordering': ['display_order', 'display_name'],
                'indexes': [models.Index(fields=['name'], name='idx_tech_cat_name'), models.Index(fields=['is_active'], name='idx_tech_cat_active'), models.Index(fields=['display_order'], name='idx_tech_cat_order')],
            },
        ),
        migrations.CreateModel(
            name='Technology',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('logo_url', models.URLField(blank=True, help_text='URL to the technology logo/icon', max_length=500, null=True, validators=[django.core.validators.URLValidator()])),
                ('description', models.TextField(blank=True, help_text='Description of our experience with this technology', null=True)),
                ('proficiency_level', models.CharField(choices=[('beginner', 'Beginner'), ('intermediate', 'Intermediate'), ('advanced', 'Advanced'), ('expert', 'Expert')], default='intermediate', max_length=20)),
                ('years_experience', models.IntegerField(blank=True, help_text='Number of years of experience with this technology', null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(50)])),
                ('is_featured', models.BooleanField(db_index=True, default=False, help_text='Whether this technology should be featured prominently')),
                ('is_active', models.BooleanField(db_index=True, default=True, help_text='Whether this technology should be displayed')),
                ('display_order', models.IntegerField(db_index=True, default=0, help_text='Order within the category (lower numbers first)')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='technologies', to='technologies.technologycategory')),
            ],
            options={
                'verbose_name': 'Technology',
                'verbose_name_plural': 'Technologies',
                'db_table': 'technologies',
                'ordering': ['category__display_order', 'display_order', 'name'],
                'indexes': [models.Index(fields=['category'], name='idx_tech_category'), models.Index(fields=['is_featured'], name='idx_tech_featured'), models.Index(fields=['is_active'], name='idx_tech_active'), models.Index(fields=['display_order'], name='idx_tech_order')],
            },
        ),
    ]
