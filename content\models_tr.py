"""
Turkish models for content management.

These models mirror the English content models but store data in the Turkish database
to comply with Turkish data storage laws.
"""

from django.db import models
from django.core.validators import URLValidator, MinValueValidator, MaxValueValidator
from django.utils import timezone
from core.models import TurkishBaseModel, TurkishModelManager
import json


class AboutUsTR(TurkishBaseModel):
    """
    Turkish version of AboutUs model for managing About Us page content.
    Corresponds to the about_us_tr table in the Turkish database.
    """

    title = models.CharField(max_length=200, verbose_name="Başlık")
    description = models.TextField(verbose_name="Açıklama")
    main_image = models.URLField(
        max_length=500, blank=True, null=True,
        validators=[URLValidator()],
        verbose_name="Ana Görsel"
    )
    secondary_image = models.URLField(
        max_length=500, blank=True, null=True,
        validators=[URLValidator()],
        verbose_name="İkincil Görsel"
    )
    company_name = models.CharField(max_length=100, verbose_name="Şirket Adı")
    established_year = models.IntegerField(
        blank=True, null=True,
        validators=[MinValueValidator(1900), MaxValueValidator(2100)],
        verbose_name="Kuruluş Yılı"
    )
    mission_statement = models.TextField(blank=True, null=True, verbose_name="Misyon")
    vision_statement = models.TextField(blank=True, null=True, verbose_name="Vizyon")
    values = models.TextField(blank=True, null=True, verbose_name="Değerler")
    is_active = models.BooleanField(default=True, db_index=True, verbose_name="Aktif")
    display_order = models.IntegerField(default=0, db_index=True, verbose_name="Görüntüleme Sırası")
    created_at = models.DateTimeField(default=timezone.now, verbose_name="Oluşturulma Tarihi")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Güncellenme Tarihi")

    objects = TurkishModelManager()

    class Meta:
        db_table = 'about_us_tr'
        verbose_name = 'Hakkımızda'
        verbose_name_plural = 'Hakkımızda'
        ordering = ['display_order', '-created_at']
        indexes = [
            models.Index(fields=['is_active'], name='idx_about_tr_active'),
            models.Index(fields=['display_order'], name='idx_about_tr_order'),
        ]

    def __str__(self):
        return f"{self.title} - {self.company_name}"


class ServiceTR(TurkishBaseModel):
    """
    Turkish version of Service model for main services offered by the company.
    Corresponds to the services_tr table in the Turkish database.
    """

    CATEGORY_CHOICES = [
        ('web_development', 'Web Geliştirme'),
        ('mobile_development', 'Mobil Geliştirme'),
        ('ui_ux_design', 'UI/UX Tasarım'),
        ('consulting', 'Danışmanlık'),
        ('maintenance', 'Bakım ve Destek'),
        ('custom_software', 'Özel Yazılım'),
        ('other', 'Diğer'),
    ]

    title = models.CharField(max_length=150, verbose_name="Başlık")
    category = models.CharField(
        max_length=50,
        choices=CATEGORY_CHOICES,
        default='other',
        db_index=True,
        help_text="Gruplama ve filtreleme için hizmet kategorisi",
        verbose_name="Kategori"
    )
    short_description = models.CharField(
        max_length=300, blank=True, null=True,
        verbose_name="Kısa Açıklama"
    )
    full_description = models.TextField(blank=True, null=True, verbose_name="Detaylı Açıklama")
    icon = models.CharField(
        max_length=100, blank=True, null=True,
        help_text="CSS sınıfı veya ikon tanımlayıcısı",
        verbose_name="İkon"
    )
    image = models.URLField(
        max_length=500, blank=True, null=True,
        validators=[URLValidator()],
        verbose_name="Görsel"
    )
    features = models.JSONField(
        blank=True, null=True,
        help_text="Hizmet özelliklerinin JSON dizisi",
        verbose_name="Özellikler"
    )
    price_range = models.CharField(max_length=50, blank=True, null=True, verbose_name="Fiyat Aralığı")
    delivery_time = models.CharField(max_length=100, blank=True, null=True, verbose_name="Teslimat Süresi")
    is_featured = models.BooleanField(default=False, db_index=True, verbose_name="Öne Çıkan")
    is_active = models.BooleanField(default=True, db_index=True, verbose_name="Aktif")
    display_order = models.IntegerField(default=0, db_index=True, verbose_name="Görüntüleme Sırası")
    created_at = models.DateTimeField(default=timezone.now, verbose_name="Oluşturulma Tarihi")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Güncellenme Tarihi")

    objects = TurkishModelManager()

    class Meta:
        db_table = 'services_tr'
        verbose_name = 'Hizmet'
        verbose_name_plural = 'Hizmetler'
        ordering = ['display_order', 'title']
        indexes = [
            models.Index(fields=['category'], name='idx_service_tr_category'),
            models.Index(fields=['is_featured'], name='idx_service_tr_featured'),
            models.Index(fields=['is_active'], name='idx_service_tr_active'),
            models.Index(fields=['display_order'], name='idx_service_tr_order'),
        ]

    def __str__(self):
        return f"{self.title} ({self.get_category_display()})"

    @property
    def features_list(self):
        """Return features as a list."""
        if self.features:
            try:
                return json.loads(self.features) if isinstance(self.features, str) else self.features
            except (json.JSONDecodeError, TypeError):
                return []
        return []


class AdditionalServiceTR(TurkishBaseModel):
    """
    Turkish version of AdditionalService model for supplementary services.
    Corresponds to the additional_services_tr table in the Turkish database.
    """

    title = models.CharField(max_length=150, verbose_name="Başlık")
    subtitle = models.CharField(max_length=200, blank=True, null=True, verbose_name="Alt Başlık")
    description = models.TextField(blank=True, null=True, verbose_name="Açıklama")
    icon = models.CharField(
        max_length=100, blank=True, null=True,
        help_text="CSS sınıfı veya ikon tanımlayıcısı",
        verbose_name="İkon"
    )
    image = models.URLField(
        max_length=500, blank=True, null=True,
        validators=[URLValidator()],
        verbose_name="Görsel"
    )
    features = models.JSONField(
        blank=True, null=True,
        help_text="Ek hizmet özelliklerinin JSON dizisi",
        verbose_name="Özellikler"
    )
    price_info = models.CharField(max_length=100, blank=True, null=True, verbose_name="Fiyat Bilgisi")
    is_popular = models.BooleanField(default=False, db_index=True, verbose_name="Popüler")
    is_active = models.BooleanField(default=True, db_index=True, verbose_name="Aktif")
    display_order = models.IntegerField(default=0, db_index=True, verbose_name="Görüntüleme Sırası")
    created_at = models.DateTimeField(default=timezone.now, verbose_name="Oluşturulma Tarihi")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Güncellenme Tarihi")

    objects = TurkishModelManager()

    class Meta:
        db_table = 'additional_services_tr'
        verbose_name = 'Ek Hizmet'
        verbose_name_plural = 'Ek Hizmetler'
        ordering = ['display_order', 'title']
        indexes = [
            models.Index(fields=['is_popular'], name='idx_add_service_tr_popular'),
            models.Index(fields=['is_active'], name='idx_add_service_tr_active'),
            models.Index(fields=['display_order'], name='idx_add_service_tr_order'),
        ]

    def __str__(self):
        return self.title

    @property
    def features_list(self):
        """Return features as a list."""
        if self.features:
            try:
                return json.loads(self.features) if isinstance(self.features, str) else self.features
            except (json.JSONDecodeError, TypeError):
                return []
        return []



class FeaturedResourceTR(TurkishBaseModel):
    """
    Turkish version of FeaturedResource model for showcasing resources.
    Corresponds to the featured_resources_tr table in the Turkish database.
    """

    RESOURCE_TYPE_CHOICES = [
        ('blog_post', 'Blog Yazısı'),
        ('case_study', 'Vaka Çalışması'),
        ('whitepaper', 'Teknik Rapor'),
        ('tutorial', 'Eğitim'),
        ('webinar', 'Webinar'),
        ('ebook', 'E-kitap'),
        ('infographic', 'İnfografik'),
        ('video', 'Video'),
        ('podcast', 'Podcast'),
        ('other', 'Diğer'),
    ]

    DIFFICULTY_CHOICES = [
        ('beginner', 'Başlangıç'),
        ('intermediate', 'Orta'),
        ('advanced', 'İleri'),
    ]

    title = models.CharField(max_length=200, verbose_name="Başlık")
    description = models.TextField(blank=True, null=True, verbose_name="Açıklama")
    image_url = models.URLField(
        max_length=500,
        validators=[URLValidator()],
        verbose_name="Görsel URL"
    )
    resource_type = models.CharField(
        max_length=50,
        choices=RESOURCE_TYPE_CHOICES,
        db_index=True,
        verbose_name="Kaynak Türü"
    )
    content_url = models.URLField(
        max_length=500,
        validators=[URLValidator()],
        help_text="Tam kaynak içeriğine bağlantı",
        verbose_name="İçerik URL"
    )
    external_link = models.BooleanField(
        default=False,
        help_text="Harici siteye bağlantı varsa True",
        verbose_name="Harici Bağlantı"
    )
    reading_time = models.IntegerField(
        blank=True, null=True,
        help_text="Tahmini okuma süresi (dakika)",
        validators=[MinValueValidator(1)],
        verbose_name="Okuma Süresi"
    )
    difficulty_level = models.CharField(
        max_length=20,
        choices=DIFFICULTY_CHOICES,
        blank=True, null=True,
        verbose_name="Zorluk Seviyesi"
    )
    tags = models.JSONField(
        blank=True, null=True,
        help_text="Kategorizasyon için etiketlerin JSON dizisi",
        verbose_name="Etiketler"
    )
    author = models.CharField(max_length=100, blank=True, null=True, verbose_name="Yazar")
    published_date = models.DateField(blank=True, null=True, db_index=True, verbose_name="Yayın Tarihi")
    is_featured = models.BooleanField(default=True, db_index=True, verbose_name="Öne Çıkan")
    is_active = models.BooleanField(default=True, db_index=True, verbose_name="Aktif")
    display_order = models.IntegerField(default=0, db_index=True, verbose_name="Görüntüleme Sırası")
    view_count = models.IntegerField(default=0, verbose_name="Görüntülenme Sayısı")
    created_at = models.DateTimeField(default=timezone.now, verbose_name="Oluşturulma Tarihi")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Güncellenme Tarihi")

    objects = TurkishModelManager()

    class Meta:
        db_table = 'featured_resources_tr'
        verbose_name = 'Öne Çıkan Kaynak'
        verbose_name_plural = 'Öne Çıkan Kaynaklar'
        ordering = ['display_order', '-published_date', '-created_at']
        indexes = [
            models.Index(fields=['resource_type'], name='idx_resource_tr_type'),
            models.Index(fields=['published_date'], name='idx_resource_tr_published'),
            models.Index(fields=['is_featured'], name='idx_resource_tr_featured'),
            models.Index(fields=['is_active'], name='idx_resource_tr_active'),
            models.Index(fields=['display_order'], name='idx_resource_tr_order'),
        ]

    def __str__(self):
        return f"{self.title} ({self.get_resource_type_display()})"

    @property
    def tags_list(self):
        """Return tags as a list."""
        if self.tags:
            try:
                return json.loads(self.tags) if isinstance(self.tags, str) else self.tags
            except (json.JSONDecodeError, TypeError):
                return []
        return []

    def increment_view_count(self):
        """Increment the view count for this resource."""
        self.view_count += 1
        self.save(update_fields=['view_count'])


class SocialMediaLinkTR(TurkishBaseModel):
    """
    Turkish version of SocialMediaLink model for managing social media presence.
    Corresponds to the social_media_links_tr table in the Turkish database.
    """

    PLATFORM_CHOICES = [
        ('linkedin', 'LinkedIn'),
        ('facebook', 'Facebook'),
        ('instagram', 'Instagram'),
        ('twitter', 'Twitter'),
        ('youtube', 'YouTube'),
        ('github', 'GitHub'),
        ('dribbble', 'Dribbble'),
    ]

    platform = models.CharField(
        max_length=50,
        choices=PLATFORM_CHOICES,
        unique=True,
        db_index=True,
        verbose_name="Platform"
    )
    display_name = models.CharField(max_length=100, verbose_name="Görüntülenen Ad")
    url = models.URLField(
        max_length=500,
        validators=[URLValidator()],
        verbose_name="URL"
    )
    icon_class = models.CharField(
        max_length=100,
        blank=True, null=True,
        help_text="İkon görüntüleme için CSS sınıfı",
        verbose_name="İkon Sınıfı"
    )
    icon_svg = models.TextField(
        blank=True, null=True,
        help_text="Özel ikonlar kullanılıyorsa SVG ikon kodu",
        verbose_name="SVG İkon"
    )
    is_active = models.BooleanField(default=True, db_index=True, verbose_name="Aktif")
    display_order = models.IntegerField(default=0, db_index=True, verbose_name="Görüntüleme Sırası")
    follower_count = models.IntegerField(
        blank=True, null=True,
        help_text="İsteğe bağlı: takipçi sayılarını görüntülemek için",
        validators=[MinValueValidator(0)],
        verbose_name="Takipçi Sayısı"
    )
    created_at = models.DateTimeField(default=timezone.now, verbose_name="Oluşturulma Tarihi")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Güncellenme Tarihi")

    objects = TurkishModelManager()

    class Meta:
        db_table = 'social_media_links_tr'
        verbose_name = 'Sosyal Medya Bağlantısı'
        verbose_name_plural = 'Sosyal Medya Bağlantıları'
        ordering = ['display_order', 'platform']
        indexes = [
            models.Index(fields=['platform'], name='idx_social_tr_platform'),
            models.Index(fields=['is_active'], name='idx_social_tr_active'),
            models.Index(fields=['display_order'], name='idx_social_tr_order'),
        ]

    def __str__(self):
        return f"{self.get_platform_display()} - {self.display_name}"

    @property
    def formatted_follower_count(self):
        """Return formatted follower count."""
        if not self.follower_count:
            return None

        if self.follower_count >= 1000000:
            return f"{self.follower_count / 1000000:.1f}M"
        elif self.follower_count >= 1000:
            return f"{self.follower_count / 1000:.1f}K"
        else:
            return str(self.follower_count)