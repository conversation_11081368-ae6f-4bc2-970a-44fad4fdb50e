# Generated by Django 5.2.4 on 2025-07-24 11:45

import django.core.validators
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='DemoRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.<PERSON>r<PERSON>ield(max_length=120)),
                ('last_name', models.Char<PERSON>ield(max_length=120)),
                ('email', models.EmailField(max_length=190, validators=[django.core.validators.EmailValidator()])),
                ('phone', models.CharField(blank=True, max_length=30, null=True, validators=[django.core.validators.RegexValidator(message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed.", regex='^\\+?1?\\d{9,15}$')])),
                ('company_name', models.CharField(max_length=150)),
                ('job_title', models.CharField(blank=True, max_length=100, null=True)),
                ('company_size', models.CharField(blank=True, choices=[('1-10', '1-10 employees'), ('11-50', '11-50 employees'), ('51-200', '51-200 employees'), ('201-1000', '201-1000 employees'), ('1000+', '1000+ employees')], max_length=50, null=True)),
                ('industry', models.CharField(blank=True, max_length=100, null=True)),
                ('project_type', models.CharField(blank=True, choices=[('web_app', 'Web Application'), ('mobile_app', 'Mobile Application'), ('api', 'API Development'), ('custom_software', 'Custom Software'), ('other', 'Other')], max_length=100, null=True)),
                ('budget_range', models.CharField(blank=True, choices=[('under_10k', 'Under $10,000'), ('10k_25k', '$10,000 - $25,000'), ('25k_50k', '$25,000 - $50,000'), ('50k_100k', '$50,000 - $100,000'), ('100k_plus', '$100,000+')], max_length=50, null=True)),
                ('timeline', models.CharField(blank=True, choices=[('asap', 'ASAP'), ('1_month', '1 Month'), ('3_months', '3 Months'), ('6_months', '6 Months'), ('1_year_plus', '1 Year+')], max_length=50, null=True)),
                ('project_description', models.TextField()),
                ('specific_requirements', models.TextField(blank=True, null=True)),
                ('preferred_demo_date', models.DateField(blank=True, null=True)),
                ('preferred_demo_time', models.CharField(blank=True, choices=[('morning', 'Morning'), ('afternoon', 'Afternoon'), ('evening', 'Evening')], max_length=20, null=True)),
                ('how_did_you_hear', models.CharField(blank=True, choices=[('google', 'Google Search'), ('social_media', 'Social Media'), ('referral', 'Referral'), ('event', 'Event'), ('other', 'Other')], max_length=100, null=True)),
                ('status', models.CharField(choices=[('new', 'New'), ('contacted', 'Contacted'), ('demo_scheduled', 'Demo Scheduled'), ('demo_completed', 'Demo Completed'), ('proposal_sent', 'Proposal Sent'), ('closed_won', 'Closed Won'), ('closed_lost', 'Closed Lost')], db_index=True, default='new', max_length=20)),
                ('assigned_to', models.CharField(blank=True, max_length=100, null=True)),
                ('demo_scheduled_at', models.DateTimeField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(db_index=True, default=django.utils.timezone.now)),
            ],
            options={
                'verbose_name': 'Demo Request',
                'verbose_name_plural': 'Demo Requests',
                'db_table': 'demo_requests',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['email', 'created_at'], name='idx_demo_email_created_at'), models.Index(fields=['status'], name='idx_demo_status'), models.Index(fields=['preferred_demo_date'], name='idx_demo_date'), models.Index(fields=['budget_range'], name='idx_demo_budget'), models.Index(fields=['project_type'], name='idx_demo_project_type')],
            },
        ),
    ]
