"""
Management command to create and migrate Turkish database tables.

This command creates the Turkish database tables for all Turkish models
and ensures they are properly set up in the Turkish database.
"""

from django.core.management.base import BaseCommand
from django.db import connections
from django.apps import apps


class Command(BaseCommand):
    help = 'Create and migrate Turkish database tables'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show SQL statements without executing them',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        self.stdout.write(
            self.style.SUCCESS('Starting Turkish database migration...')
        )

        # Get the Turkish database connection
        turkish_db = connections['turkish']
        
        # Import Turkish models
        turkish_models = self.get_turkish_models()
        
        if not turkish_models:
            self.stdout.write(
                self.style.WARNING('No Turkish models found.')
            )
            return

        # Create tables for Turkish models
        with turkish_db.schema_editor() as schema_editor:
            for model in turkish_models:
                table_name = model._meta.db_table
                
                if dry_run:
                    self.stdout.write(f'Would create table: {table_name}')
                    continue
                
                try:
                    # Check if table already exists
                    if self.table_exists(turkish_db, table_name):
                        self.stdout.write(
                            self.style.WARNING(f'Table {table_name} already exists, skipping...')
                        )
                        continue
                    
                    # Create the table
                    schema_editor.create_model(model)
                    self.stdout.write(
                        self.style.SUCCESS(f'Created table: {table_name}')
                    )
                    
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'Error creating table {table_name}: {str(e)}')
                    )

        if not dry_run:
            self.stdout.write(
                self.style.SUCCESS('Turkish database migration completed!')
            )

    def get_turkish_models(self):
        """Get all Turkish models from the apps."""
        turkish_models = []
        
        # Import the Turkish model modules
        try:
            from content.models_tr import (
                AboutUsTR, ServiceTR, AdditionalServiceTR, 
                FeaturedResourceTR, SocialMediaLinkTR
            )
            turkish_models.extend([
                AboutUsTR, ServiceTR, AdditionalServiceTR, 
                FeaturedResourceTR, SocialMediaLinkTR
            ])
        except ImportError as e:
            self.stdout.write(
                self.style.WARNING(f'Could not import content Turkish models: {e}')
            )

        try:
            from team.models_tr import TeamMemberTR
            turkish_models.append(TeamMemberTR)
        except ImportError as e:
            self.stdout.write(
                self.style.WARNING(f'Could not import team Turkish models: {e}')
            )

        try:
            from technologies.models_tr import TechnologyCategoryTR, TechnologyTR
            turkish_models.extend([TechnologyCategoryTR, TechnologyTR])
        except ImportError as e:
            self.stdout.write(
                self.style.WARNING(f'Could not import technologies Turkish models: {e}')
            )

        try:
            from demos.models_tr import DemoRequestTR
            turkish_models.append(DemoRequestTR)
        except ImportError as e:
            self.stdout.write(
                self.style.WARNING(f'Could not import demos Turkish models: {e}')
            )

        return turkish_models

    def table_exists(self, connection, table_name):
        """Check if a table exists in the database."""
        with connection.cursor() as cursor:
            # For SQLite
            if connection.vendor == 'sqlite':
                cursor.execute(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                    [table_name]
                )
                return cursor.fetchone() is not None

            # For PostgreSQL
            elif connection.vendor == 'postgresql':
                cursor.execute(
                    "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = %s)",
                    [table_name]
                )
                return cursor.fetchone()[0]

            # For MySQL
            elif connection.vendor == 'mysql':
                cursor.execute("SHOW TABLES LIKE %s", [table_name])
                return cursor.fetchone() is not None

            # Default fallback
            else:
                try:
                    cursor.execute(f"SELECT 1 FROM {connection.ops.quote_name(table_name)} LIMIT 1")
                    return True
                except:
                    return False
