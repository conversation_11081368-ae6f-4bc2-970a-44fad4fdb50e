from django.contrib import admin
from django.utils.html import format_html
from .models import AboutUs, Service, AdditionalService, FeaturedResource, SocialMediaLink


@admin.register(AboutUs)
class AboutUsAdmin(admin.ModelAdmin):
    """Admin interface for AboutUs model."""

    list_display = ['title', 'company_name', 'established_year', 'is_active', 'display_order', 'created_at']
    list_filter = ['is_active', 'established_year', 'created_at']
    search_fields = ['title', 'company_name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['display_order', '-created_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'company_name', 'established_year', 'description')
        }),
        ('Images', {
            'fields': ('main_image', 'secondary_image')
        }),
        ('Company Details', {
            'fields': ('mission_statement', 'vision_statement', 'values')
        }),
        ('Display Settings', {
            'fields': ('is_active', 'display_order')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    """Admin interface for Service model."""

    list_display = ['title', 'category', 'price_range', 'duration', 'is_featured', 'is_active', 'display_order']
    list_filter = ['category', 'is_featured', 'is_active', 'created_at']
    search_fields = ['title', 'short_description', 'full_description']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['display_order', 'title']

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'category', 'short_description', 'full_description')
        }),
        ('Visual Elements', {
            'fields': ('icon', 'image')
        }),
        ('Service Details', {
            'fields': ('features', 'price_range', 'duration')
        }),
        ('Display Settings', {
            'fields': ('is_featured', 'is_active', 'display_order')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(AdditionalService)
class AdditionalServiceAdmin(admin.ModelAdmin):
    """Admin interface for AdditionalService model."""

    list_display = ['title', 'category', 'is_active', 'display_order']
    list_filter = ['category', 'is_active', 'created_at']
    search_fields = ['title', 'subtitle', 'description']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['category', 'display_order', 'title']

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'subtitle', 'description', 'category')
        }),
        ('Visual Elements', {
            'fields': ('icon', 'image')
        }),
        ('Features', {
            'fields': ('features',)
        }),
        ('Display Settings', {
            'fields': ('is_active', 'display_order')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(FeaturedResource)
class FeaturedResourceAdmin(admin.ModelAdmin):
    """Admin interface for FeaturedResource model."""

    list_display = [
        'title', 'resource_type', 'author', 'difficulty_level',
        'view_count', 'is_featured', 'is_active', 'published_date'
    ]
    list_filter = [
        'resource_type', 'difficulty_level', 'is_featured',
        'is_active', 'external_link', 'published_date', 'created_at'
    ]
    search_fields = ['title', 'description', 'author']
    readonly_fields = ['created_at', 'updated_at', 'view_count']
    ordering = ['display_order', '-published_date']
    date_hierarchy = 'published_date'

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'description', 'author', 'published_date')
        }),
        ('Content Details', {
            'fields': ('resource_type', 'difficulty_level', 'reading_time', 'tags')
        }),
        ('Links and Media', {
            'fields': ('content_url', 'external_link', 'image_url')
        }),
        ('Display Settings', {
            'fields': ('is_featured', 'is_active', 'display_order')
        }),
        ('Analytics', {
            'fields': ('view_count',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_as_featured', 'mark_as_not_featured']

    def mark_as_featured(self, request, queryset):
        """Admin action to mark selected resources as featured."""
        updated = queryset.update(is_featured=True)
        self.message_user(request, f'{updated} resources marked as featured.')
    mark_as_featured.short_description = "Mark selected resources as featured"

    def mark_as_not_featured(self, request, queryset):
        """Admin action to mark selected resources as not featured."""
        updated = queryset.update(is_featured=False)
        self.message_user(request, f'{updated} resources marked as not featured.')
    mark_as_not_featured.short_description = "Mark selected resources as not featured"


@admin.register(SocialMediaLink)
class SocialMediaLinkAdmin(admin.ModelAdmin):
    """Admin interface for SocialMediaLink model."""

    list_display = [
        'platform', 'display_name', 'formatted_follower_count',
        'is_active', 'display_order'
    ]
    list_filter = ['platform', 'is_active', 'created_at']
    search_fields = ['platform', 'display_name', 'url']
    readonly_fields = ['created_at', 'updated_at', 'formatted_follower_count']
    ordering = ['display_order', 'platform']

    fieldsets = (
        ('Platform Information', {
            'fields': ('platform', 'display_name', 'url')
        }),
        ('Visual Elements', {
            'fields': ('icon_class', 'icon_svg')
        }),
        ('Analytics', {
            'fields': ('follower_count', 'formatted_follower_count')
        }),
        ('Display Settings', {
            'fields': ('is_active', 'display_order')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def formatted_follower_count(self, obj):
        """Display formatted follower count."""
        return obj.formatted_follower_count or 'N/A'
    formatted_follower_count.short_description = 'Followers'
