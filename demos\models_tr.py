"""
Turkish models for demo request management.

These models mirror the English demo models but store data in the Turkish database
to comply with Turkish data storage laws.
"""

from django.db import models
from django.core.validators import EmailValidator, RegexValidator
from django.utils import timezone
from core.models import TurkishBaseModel, TurkishModelManager


class DemoRequestTR(TurkishBaseModel):
    """
    Turkish version of DemoRequest model for handling demo request submissions and lead management.
    Corresponds to the demo_requests_tr table in the Turkish database.
    """

    # Company size choices in Turkish
    COMPANY_SIZE_CHOICES = [
        ('1-10', '1-10 çalışan'),
        ('11-50', '11-50 çalışan'),
        ('51-200', '51-200 çalışan'),
        ('201-1000', '201-1000 çalışan'),
        ('1000+', '1000+ çalışan'),
    ]

    # Project type choices in Turkish
    PROJECT_TYPE_CHOICES = [
        ('web_app', 'Web Uygulaması'),
        ('mobile_app', '<PERSON>bil Uygulama'),
        ('api', 'API Geliştirme'),
        ('custom_software', '<PERSON>zel Yazılım'),
        ('other', 'Diğer'),
    ]

    # Budget range choices in Turkish
    BUDGET_RANGE_CHOICES = [
        ('under_10k', '10.000 TL altı'),
        ('10k_25k', '10.000 - 25.000 TL'),
        ('25k_50k', '25.000 - 50.000 TL'),
        ('50k_100k', '50.000 - 100.000 TL'),
        ('100k_250k', '100.000 - 250.000 TL'),
        ('250k_500k', '250.000 - 500.000 TL'),
        ('500k_plus', '500.000 TL üzeri'),
        ('discuss', 'Görüşülür'),
    ]

    # Timeline choices in Turkish
    TIMELINE_CHOICES = [
        ('asap', 'En kısa sürede'),
        ('1_month', '1 ay içinde'),
        ('2_3_months', '2-3 ay içinde'),
        ('3_6_months', '3-6 ay içinde'),
        ('6_months_plus', '6 ay sonra'),
        ('flexible', 'Esnek'),
    ]

    # Demo time preferences in Turkish
    DEMO_TIME_CHOICES = [
        ('morning', 'Sabah (09:00-12:00)'),
        ('afternoon', 'Öğleden sonra (13:00-17:00)'),
        ('evening', 'Akşam (18:00-20:00)'),
        ('flexible', 'Esnek'),
    ]

    # How did you hear about us choices in Turkish
    HEAR_ABOUT_CHOICES = [
        ('google_search', 'Google Arama'),
        ('social_media', 'Sosyal Medya'),
        ('referral', 'Referans'),
        ('linkedin', 'LinkedIn'),
        ('website', 'Web Sitesi'),
        ('advertisement', 'Reklam'),
        ('event', 'Etkinlik'),
        ('other', 'Diğer'),
    ]

    # Status choices in Turkish
    STATUS_CHOICES = [
        ('new', 'Yeni'),
        ('contacted', 'İletişim Kuruldu'),
        ('demo_scheduled', 'Demo Planlandı'),
        ('demo_completed', 'Demo Tamamlandı'),
        ('proposal_sent', 'Teklif Gönderildi'),
        ('negotiating', 'Müzakere'),
        ('won', 'Kazanıldı'),
        ('lost', 'Kaybedildi'),
        ('on_hold', 'Beklemede'),
    ]

    # Contact information
    first_name = models.CharField(max_length=50, verbose_name="Ad")
    last_name = models.CharField(max_length=50, verbose_name="Soyad")
    email = models.EmailField(
        max_length=190,
        validators=[EmailValidator()],
        db_index=True,
        verbose_name="E-posta"
    )
    phone = models.CharField(
        max_length=20,
        blank=True, null=True,
        validators=[RegexValidator(
            regex=r'^\+?1?\d{9,15}$',
            message="Telefon numarası geçerli bir formatta olmalıdır."
        )],
        verbose_name="Telefon"
    )

    # Company information
    company_name = models.CharField(max_length=100, verbose_name="Şirket Adı")
    company_website = models.URLField(
        max_length=200,
        blank=True, null=True,
        verbose_name="Şirket Web Sitesi"
    )
    company_size = models.CharField(
        max_length=20,
        choices=COMPANY_SIZE_CHOICES,
        blank=True, null=True,
        verbose_name="Şirket Büyüklüğü"
    )
    job_title = models.CharField(max_length=100, blank=True, null=True, verbose_name="İş Unvanı")

    # Project details
    project_type = models.CharField(
        max_length=100,
        choices=PROJECT_TYPE_CHOICES,
        blank=True, null=True,
        verbose_name="Proje Türü"
    )
    budget_range = models.CharField(
        max_length=50,
        choices=BUDGET_RANGE_CHOICES,
        blank=True, null=True,
        verbose_name="Bütçe Aralığı"
    )
    timeline = models.CharField(
        max_length=50,
        choices=TIMELINE_CHOICES,
        blank=True, null=True,
        verbose_name="Zaman Çizelgesi"
    )
    project_description = models.TextField(verbose_name="Proje Açıklaması")
    specific_requirements = models.TextField(
        blank=True, null=True,
        verbose_name="Özel Gereksinimler"
    )

    # Demo scheduling
    preferred_demo_date = models.DateField(
        blank=True, null=True,
        verbose_name="Tercih Edilen Demo Tarihi"
    )
    preferred_demo_time = models.CharField(
        max_length=20,
        choices=DEMO_TIME_CHOICES,
        blank=True, null=True,
        verbose_name="Tercih Edilen Demo Saati"
    )

    # Marketing attribution
    how_did_you_hear = models.CharField(
        max_length=100,
        choices=HEAR_ABOUT_CHOICES,
        blank=True, null=True,
        verbose_name="Bizi Nasıl Duydunuz"
    )

    # Lead management
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='new',
        db_index=True,
        verbose_name="Durum"
    )
    notes = models.TextField(
        blank=True, null=True,
        help_text="İç notlar ve takip bilgileri",
        verbose_name="Notlar"
    )

    # Timestamps
    created_at = models.DateTimeField(default=timezone.now, verbose_name="Oluşturulma Tarihi")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Güncellenme Tarihi")

    objects = TurkishModelManager()

    class Meta:
        db_table = 'demo_requests_tr'
        verbose_name = 'Demo Talebi'
        verbose_name_plural = 'Demo Talepleri'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['email', 'created_at'], name='idx_demo_tr_email_created_at'),
            models.Index(fields=['status'], name='idx_demo_tr_status'),
            models.Index(fields=['preferred_demo_date'], name='idx_demo_tr_date'),
            models.Index(fields=['budget_range'], name='idx_demo_tr_budget'),
            models.Index(fields=['project_type'], name='idx_demo_tr_project_type'),
        ]

    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.company_name} ({self.get_status_display()})"

    @property
    def full_name(self):
        """Return the full name of the contact."""
        return f"{self.first_name} {self.last_name}"

    @property
    def is_high_value_lead(self):
        """Determine if this is a high-value lead based on budget and company size."""
        high_value_budgets = ['100k_250k', '250k_500k', '500k_plus']
        large_companies = ['201-1000', '1000+']
        
        return (self.budget_range in high_value_budgets or 
                self.company_size in large_companies)

    @property
    def urgency_level(self):
        """Return urgency level based on timeline."""
        urgency_map = {
            'asap': 'Yüksek',
            '1_month': 'Yüksek',
            '2_3_months': 'Orta',
            '3_6_months': 'Düşük',
            '6_months_plus': 'Düşük',
            'flexible': 'Düşük',
        }
        return urgency_map.get(self.timeline, 'Orta')

    @property
    def contact_info(self):
        """Return formatted contact information."""
        info = {
            'name': self.full_name,
            'email': self.email,
            'phone': self.phone,
            'company': self.company_name,
            'title': self.job_title,
        }
        return {k: v for k, v in info.items() if v}

    def get_status_color(self):
        """Return color code for status display."""
        color_map = {
            'new': '#007bff',           # Blue
            'contacted': '#17a2b8',     # Info
            'demo_scheduled': '#ffc107', # Warning
            'demo_completed': '#6f42c1', # Purple
            'proposal_sent': '#fd7e14',  # Orange
            'negotiating': '#e83e8c',    # Pink
            'won': '#28a745',           # Success
            'lost': '#dc3545',          # Danger
            'on_hold': '#6c757d',       # Secondary
        }
        return color_map.get(self.status, '#6c757d')

    def get_priority_score(self):
        """Calculate priority score based on various factors."""
        score = 0
        
        # Budget score
        budget_scores = {
            'under_10k': 1,
            '10k_25k': 2,
            '25k_50k': 3,
            '50k_100k': 4,
            '100k_250k': 6,
            '250k_500k': 8,
            '500k_plus': 10,
            'discuss': 5,
        }
        score += budget_scores.get(self.budget_range, 0)
        
        # Company size score
        size_scores = {
            '1-10': 1,
            '11-50': 3,
            '51-200': 5,
            '201-1000': 7,
            '1000+': 10,
        }
        score += size_scores.get(self.company_size, 0)
        
        # Timeline urgency score
        timeline_scores = {
            'asap': 10,
            '1_month': 8,
            '2_3_months': 5,
            '3_6_months': 3,
            '6_months_plus': 1,
            'flexible': 2,
        }
        score += timeline_scores.get(self.timeline, 0)
        
        return score

    @classmethod
    def get_leads_by_status(cls):
        """Return count of leads grouped by status."""
        from django.db.models import Count
        return cls.objects.values('status').annotate(count=Count('id')).order_by('status')

    @classmethod
    def get_high_priority_leads(cls, limit=10):
        """Return high priority leads based on priority score."""
        leads = cls.objects.filter(status__in=['new', 'contacted', 'demo_scheduled'])
        # Sort by priority score (calculated in Python since it's complex)
        sorted_leads = sorted(leads, key=lambda x: x.get_priority_score(), reverse=True)
        return sorted_leads[:limit]

    def clean(self):
        """Custom validation for the model."""
        from django.core.exceptions import ValidationError
        
        # Validate demo date is not in the past
        if self.preferred_demo_date:
            from django.utils import timezone
            if self.preferred_demo_date < timezone.now().date():
                raise ValidationError("Demo tarihi geçmişte olamaz.")
