"""
Management command to create Turkish database tables using raw SQL.

This command creates the Turkish database tables manually to avoid
Django migration system complexities with our multi-database setup.
"""

from django.core.management.base import BaseCommand
from django.db import connections


class Command(BaseCommand):
    help = 'Create Turkish database tables using raw SQL'

    def add_arguments(self, parser):
        parser.add_argument(
            '--drop-existing',
            action='store_true',
            help='Drop existing tables before creating new ones',
        )

    def handle(self, *args, **options):
        drop_existing = options['drop_existing']
        
        self.stdout.write(
            self.style.SUCCESS('Creating Turkish database tables...')
        )

        # Get the Turkish database connection
        turkish_db = connections['turkish']
        
        # SQL statements for creating Turkish tables
        sql_statements = self.get_create_table_sql()
        
        with turkish_db.cursor() as cursor:
            for table_name, sql in sql_statements.items():
                try:
                    if drop_existing:
                        cursor.execute(f"DROP TABLE IF EXISTS {table_name}")
                        self.stdout.write(f'Dropped table: {table_name}')
                    
                    cursor.execute(sql)
                    self.stdout.write(
                        self.style.SUCCESS(f'Created table: {table_name}')
                    )
                    
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'Error with table {table_name}: {str(e)}')
                    )

        self.stdout.write(
            self.style.SUCCESS('Turkish database tables created successfully!')
        )

    def get_create_table_sql(self):
        """Return SQL statements for creating Turkish tables."""
        return {
            'about_us_tr': '''
                CREATE TABLE about_us_tr (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title VARCHAR(200) NOT NULL,
                    description TEXT NOT NULL,
                    main_image VARCHAR(500),
                    secondary_image VARCHAR(500),
                    company_name VARCHAR(100) NOT NULL,
                    established_year INTEGER,
                    mission_statement TEXT,
                    vision_statement TEXT,
                    "values" TEXT,
                    is_active BOOLEAN NOT NULL DEFAULT 1,
                    display_order INTEGER NOT NULL DEFAULT 0,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL
                )
            ''',
            
            'services_tr': '''
                CREATE TABLE services_tr (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title VARCHAR(150) NOT NULL,
                    category VARCHAR(50) NOT NULL DEFAULT 'other',
                    short_description VARCHAR(300),
                    full_description TEXT,
                    icon VARCHAR(100),
                    image VARCHAR(500),
                    features TEXT,
                    price_range VARCHAR(50),
                    delivery_time VARCHAR(100),
                    is_featured BOOLEAN NOT NULL DEFAULT 0,
                    is_active BOOLEAN NOT NULL DEFAULT 1,
                    display_order INTEGER NOT NULL DEFAULT 0,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL
                )
            ''',
            
            'additional_services_tr': '''
                CREATE TABLE additional_services_tr (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title VARCHAR(150) NOT NULL,
                    subtitle VARCHAR(200),
                    description TEXT,
                    icon VARCHAR(100),
                    image VARCHAR(500),
                    features TEXT,
                    price_info VARCHAR(100),
                    is_popular BOOLEAN NOT NULL DEFAULT 0,
                    is_active BOOLEAN NOT NULL DEFAULT 1,
                    display_order INTEGER NOT NULL DEFAULT 0,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL
                )
            ''',
            
            'featured_resources_tr': '''
                CREATE TABLE featured_resources_tr (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title VARCHAR(200) NOT NULL,
                    description TEXT,
                    image_url VARCHAR(500) NOT NULL,
                    resource_type VARCHAR(50) NOT NULL,
                    content_url VARCHAR(500) NOT NULL,
                    external_link BOOLEAN NOT NULL DEFAULT 0,
                    reading_time INTEGER,
                    difficulty_level VARCHAR(20),
                    tags TEXT,
                    author VARCHAR(100),
                    published_date DATE,
                    is_featured BOOLEAN NOT NULL DEFAULT 1,
                    is_active BOOLEAN NOT NULL DEFAULT 1,
                    display_order INTEGER NOT NULL DEFAULT 0,
                    view_count INTEGER NOT NULL DEFAULT 0,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL
                )
            ''',
            
            'social_media_links_tr': '''
                CREATE TABLE social_media_links_tr (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    platform VARCHAR(50) NOT NULL UNIQUE,
                    display_name VARCHAR(100) NOT NULL,
                    url VARCHAR(500) NOT NULL,
                    icon_class VARCHAR(100),
                    icon_svg TEXT,
                    is_active BOOLEAN NOT NULL DEFAULT 1,
                    display_order INTEGER NOT NULL DEFAULT 0,
                    follower_count INTEGER,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL
                )
            ''',
            
            'team_members_tr': '''
                CREATE TABLE team_members_tr (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    full_name VARCHAR(120) NOT NULL,
                    position VARCHAR(100) NOT NULL,
                    bio TEXT,
                    profile_image VARCHAR(500),
                    email VARCHAR(190),
                    linkedin_url VARCHAR(300),
                    github_url VARCHAR(300),
                    is_active BOOLEAN NOT NULL DEFAULT 1,
                    display_order INTEGER NOT NULL DEFAULT 0,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL
                )
            ''',
            
            'technology_categories_tr': '''
                CREATE TABLE technology_categories_tr (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(100) NOT NULL UNIQUE,
                    display_name VARCHAR(120) NOT NULL,
                    description TEXT,
                    icon VARCHAR(100),
                    color_code VARCHAR(7),
                    is_active BOOLEAN NOT NULL DEFAULT 1,
                    display_order INTEGER NOT NULL DEFAULT 0,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL
                )
            ''',
            
            'technologies_tr': '''
                CREATE TABLE technologies_tr (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(100) NOT NULL,
                    logo_url VARCHAR(500),
                    category_id INTEGER NOT NULL,
                    description TEXT,
                    proficiency_level VARCHAR(20) NOT NULL DEFAULT 'intermediate',
                    years_experience INTEGER,
                    is_featured BOOLEAN NOT NULL DEFAULT 0,
                    is_active BOOLEAN NOT NULL DEFAULT 1,
                    display_order INTEGER NOT NULL DEFAULT 0,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL,
                    FOREIGN KEY (category_id) REFERENCES technology_categories_tr (id),
                    UNIQUE (name, category_id)
                )
            ''',
            
            'demo_requests_tr': '''
                CREATE TABLE demo_requests_tr (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    first_name VARCHAR(50) NOT NULL,
                    last_name VARCHAR(50) NOT NULL,
                    email VARCHAR(190) NOT NULL,
                    phone VARCHAR(20),
                    company_name VARCHAR(100) NOT NULL,
                    company_website VARCHAR(200),
                    company_size VARCHAR(20),
                    job_title VARCHAR(100),
                    project_type VARCHAR(100),
                    budget_range VARCHAR(50),
                    timeline VARCHAR(50),
                    project_description TEXT NOT NULL,
                    specific_requirements TEXT,
                    preferred_demo_date DATE,
                    preferred_demo_time VARCHAR(20),
                    how_did_you_hear VARCHAR(100),
                    status VARCHAR(20) NOT NULL DEFAULT 'new',
                    notes TEXT,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL
                )
            '''
        }
