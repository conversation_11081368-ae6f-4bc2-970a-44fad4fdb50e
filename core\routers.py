"""
Database routing for multi-language support.

This router automatically directs Turkish models to the Turkish database
and English models to the default database, ensuring compliance with
Turkish data storage laws.
"""

import threading
from django.conf import settings


# Thread-local storage for language context
_thread_local = threading.local()


def get_current_language():
    """Get the current language from thread-local storage."""
    return getattr(_thread_local, 'language', 'en')


def set_current_language(language):
    """Set the current language in thread-local storage."""
    _thread_local.language = language


def clear_language_context():
    """Clear the language context from thread-local storage."""
    if hasattr(_thread_local, 'language'):
        delattr(_thread_local, 'language')


def get_language_context():
    """Get the complete language context information."""
    return {
        'language': get_current_language(),
        'database': get_database_for_language(get_current_language()),
        'is_turkish': get_current_language() == 'tr',
        'is_english': get_current_language() == 'en',
    }


class LanguageRouter:
    """
    A router to control all database operations on models for different languages.
    
    Turkish models (ending with 'TR') are routed to the 'turkish' database.
    All other models are routed to the 'default' database.
    """
    
    # Turkish model suffixes and app labels that should use Turkish database
    TURKISH_MODEL_SUFFIXES = ('TR', 'Turkish')
    TURKISH_APP_LABELS = ('turkish_content', 'turkish_team', 'turkish_technologies', 'turkish_demos')
    
    def db_for_read(self, model, **hints):
        """Suggest the database to read from."""
        return self._get_database_for_model(model)
    
    def db_for_write(self, model, **hints):
        """Suggest the database to write to."""
        return self._get_database_for_model(model)
    
    def allow_relation(self, obj1, obj2, **hints):
        """Allow relations if models are in the same database."""
        db_set = {'default', 'turkish'}
        if obj1._state.db in db_set and obj2._state.db in db_set:
            return True
        return None
    
    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """Ensure that certain apps' models get created on the right database."""

        # Turkish models should only be created in the Turkish database
        if model_name and self._is_turkish_model(model_name, app_label):
            return db == 'turkish'

        # Turkish app labels should only be in Turkish database
        if app_label in self.TURKISH_APP_LABELS:
            return db == 'turkish'

        # Django's built-in apps should only be in the default database
        django_apps = ['admin', 'auth', 'contenttypes', 'sessions', 'messages']
        if app_label in django_apps:
            return db == 'default'

        # All other models should only be created in the default database
        if db == 'turkish':
            return False

        return db == 'default'
    
    def _get_database_for_model(self, model):
        """Determine which database to use for a given model."""
        model_name = model.__name__
        app_label = model._meta.app_label
        
        # Check if it's a Turkish model
        if self._is_turkish_model(model_name, app_label):
            return 'turkish'
        
        # Check if it's a Turkish app
        if app_label in self.TURKISH_APP_LABELS:
            return 'turkish'
        
        # Default to the default database
        return 'default'
    
    def _is_turkish_model(self, model_name, app_label):
        """Check if a model should use the Turkish database."""
        if not model_name:
            return False
        
        # Check if model name ends with Turkish suffixes
        for suffix in self.TURKISH_MODEL_SUFFIXES:
            if model_name.endswith(suffix):
                return True
        
        return False






