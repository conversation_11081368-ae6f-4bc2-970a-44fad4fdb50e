from django.contrib import admin
from django.utils.html import format_html
from .models import TeamMember


@admin.register(TeamMember)
class TeamMemberAdmin(admin.ModelAdmin):
    """Admin interface for TeamMember model."""

    list_display = [
        'full_name', 'position', 'email', 'has_social_links',
        'is_active', 'display_order', 'created_at'
    ]
    list_filter = ['is_active', 'position', 'created_at']
    search_fields = ['full_name', 'position', 'email', 'bio']
    readonly_fields = ['created_at', 'updated_at', 'initials', 'first_name', 'last_name']
    ordering = ['display_order', 'full_name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('full_name', 'position', 'bio')
        }),
        ('Contact Information', {
            'fields': ('email', 'linkedin_url', 'github_url')
        }),
        ('Profile Image', {
            'fields': ('profile_image',)
        }),
        ('Display Settings', {
            'fields': ('is_active', 'display_order')
        }),
        ('Computed Fields', {
            'fields': ('first_name', 'last_name', 'initials'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['activate_members', 'deactivate_members']

    def has_social_links(self, obj):
        """Display whether the team member has social links."""
        if obj.has_social_links:
            return format_html('<span style="color: green;">✓ Yes</span>')
        return format_html('<span style="color: orange;">✗ No</span>')
    has_social_links.short_description = 'Social Links'
    has_social_links.boolean = True

    def activate_members(self, request, queryset):
        """Admin action to activate selected team members."""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} team members activated.')
    activate_members.short_description = "Activate selected team members"

    def deactivate_members(self, request, queryset):
        """Admin action to deactivate selected team members."""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} team members deactivated.')
    deactivate_members.short_description = "Deactivate selected team members"
