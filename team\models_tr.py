"""
Turkish models for team management.

These models mirror the English team models but store data in the Turkish database
to comply with Turkish data storage laws.
"""

from django.db import models
from django.core.validators import URLValidator, EmailValidator
from django.utils import timezone
from core.models import TurkishBaseModel, TurkishModelManager


class TeamMemberTR(TurkishBaseModel):
    """
    Turkish version of TeamMember model for managing team member profiles and information.
    Corresponds to the team_members_tr table in the Turkish database.
    """

    full_name = models.CharField(max_length=120, verbose_name="Ad Soyad")
    position = models.CharField(max_length=100, verbose_name="Pozisyon")
    bio = models.TextField(blank=True, null=True, verbose_name="Biyografi")
    profile_image = models.URLField(
        max_length=500,
        blank=True, null=True,
        validators=[URLValidator()],
        help_text="Takım üyesinin profil resmi URL'si",
        verbose_name="Profil Resmi"
    )
    email = models.EmailField(
        max_length=190,
        blank=True, null=True,
        validators=[EmailValidator()],
        verbose_name="E-posta"
    )
    linkedin_url = models.URLField(
        max_length=300,
        blank=True, null=True,
        validators=[URLValidator()],
        help_text="LinkedIn profil URL'si",
        verbose_name="LinkedIn URL"
    )
    github_url = models.URLField(
        max_length=300,
        blank=True, null=True,
        validators=[URLValidator()],
        help_text="GitHub profil URL'si",
        verbose_name="GitHub URL"
    )
    is_active = models.BooleanField(
        default=True,
        db_index=True,
        help_text="Bu takım üyesinin web sitesinde görüntülenip görüntülenmeyeceği",
        verbose_name="Aktif"
    )
    display_order = models.IntegerField(
        default=0,
        db_index=True,
        help_text="Takım üyelerinin görüntülenme sırası (düşük sayılar önce)",
        verbose_name="Görüntüleme Sırası"
    )
    created_at = models.DateTimeField(default=timezone.now, verbose_name="Oluşturulma Tarihi")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Güncellenme Tarihi")

    objects = TurkishModelManager()

    class Meta:
        db_table = 'team_members_tr'
        verbose_name = 'Takım Üyesi'
        verbose_name_plural = 'Takım Üyeleri'
        ordering = ['display_order', 'full_name']
        indexes = [
            models.Index(fields=['display_order'], name='idx_team_tr_display_order'),
            models.Index(fields=['is_active'], name='idx_team_tr_is_active'),
        ]

    def __str__(self):
        return f"{self.full_name} - {self.position}"

    @property
    def first_name(self):
        """Extract first name from full name."""
        return self.full_name.split()[0] if self.full_name else ""

    @property
    def last_name(self):
        """Extract last name from full name."""
        name_parts = self.full_name.split()
        return " ".join(name_parts[1:]) if len(name_parts) > 1 else ""

    @property
    def has_social_links(self):
        """Check if the team member has any social media links."""
        return bool(self.linkedin_url or self.github_url)

    @property
    def initials(self):
        """Get initials from the full name."""
        name_parts = self.full_name.split()
        if len(name_parts) >= 2:
            return f"{name_parts[0][0]}{name_parts[-1][0]}".upper()
        elif len(name_parts) == 1:
            return name_parts[0][:2].upper()
        return "TÜ"  # Turkish initials for "Takım Üyesi"

    def get_social_links(self):
        """Return a dictionary of available social links."""
        links = {}
        if self.linkedin_url:
            links['linkedin'] = self.linkedin_url
        if self.github_url:
            links['github'] = self.github_url
        return links

    def clean(self):
        """Custom validation for the model."""
        from django.core.exceptions import ValidationError

        # Ensure at least one of the social links is provided if email is not provided
        if not self.email and not self.linkedin_url and not self.github_url:
            raise ValidationError(
                "En az bir iletişim yöntemi (e-posta, LinkedIn veya GitHub) sağlanmalıdır."
            )

    @property
    def contact_methods(self):
        """Return a list of available contact methods."""
        methods = []
        if self.email:
            methods.append({
                'type': 'email',
                'value': self.email,
                'display': 'E-posta',
                'icon': 'fas fa-envelope'
            })
        if self.linkedin_url:
            methods.append({
                'type': 'linkedin',
                'value': self.linkedin_url,
                'display': 'LinkedIn',
                'icon': 'fab fa-linkedin'
            })
        if self.github_url:
            methods.append({
                'type': 'github',
                'value': self.github_url,
                'display': 'GitHub',
                'icon': 'fab fa-github'
            })
        return methods

    @property
    def short_bio(self):
        """Return a shortened version of the bio for display purposes."""
        if not self.bio:
            return ""
        
        # Return first 150 characters with ellipsis if longer
        if len(self.bio) <= 150:
            return self.bio
        
        # Find the last space before 150 characters to avoid cutting words
        truncated = self.bio[:150]
        last_space = truncated.rfind(' ')
        if last_space > 100:  # Only truncate at word boundary if it's not too short
            truncated = truncated[:last_space]
        
        return f"{truncated}..."

    def get_expertise_areas(self):
        """
        Extract expertise areas from bio or position.
        This is a simple implementation that could be enhanced with NLP.
        """
        expertise_keywords = [
            'Python', 'Django', 'JavaScript', 'React', 'Vue.js', 'Angular',
            'Node.js', 'PHP', 'Laravel', 'Java', 'Spring', 'C#', '.NET',
            'Mobile', 'iOS', 'Android', 'Flutter', 'React Native',
            'UI/UX', 'Design', 'Frontend', 'Backend', 'Full-stack',
            'DevOps', 'AWS', 'Azure', 'Docker', 'Kubernetes',
            'Database', 'PostgreSQL', 'MySQL', 'MongoDB',
            'Machine Learning', 'AI', 'Data Science'
        ]
        
        found_expertise = []
        text_to_search = f"{self.position} {self.bio or ''}".lower()
        
        for keyword in expertise_keywords:
            if keyword.lower() in text_to_search:
                found_expertise.append(keyword)
        
        return found_expertise[:5]  # Return max 5 expertise areas

    @classmethod
    def get_active_members(cls):
        """Return queryset of active team members ordered by display_order."""
        return cls.objects.filter(is_active=True).order_by('display_order', 'full_name')

    @classmethod
    def get_featured_members(cls, limit=3):
        """Return a limited number of featured team members (first in display order)."""
        return cls.get_active_members()[:limit]
